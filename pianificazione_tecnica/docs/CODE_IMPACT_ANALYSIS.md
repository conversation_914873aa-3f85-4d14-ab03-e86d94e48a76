# Budget Management System - Analisi Impatto Codice

## 📊 Tabella Riepilogativa Funzionalità

| Funzionalità | Layer | Tipo File | Nome File |
|--------------|-------|-----------|-----------|
| **Gestione Budget** | Frontend | Component | BudgetList.tsx |
| | Frontend | Component | CreateBudgetForm.tsx |
| | Frontend | Component | BudgetDetails.tsx |
| | Frontend | Service | budgetApi.ts |
| | Frontend | Types | budget.ts |
| | Backend | Controller | BudgetController.cs |
| | Backend | Command | CreateBudgetCommand.cs |
| | Backend | Query | GetBudgetsQuery.cs |
| | Backend | Handler | CreateBudgetHandler.cs |
| | Backend | Handler | GetBudgetsHandler.cs |
| | Backend | Entity | Budget.cs |
| | Backend | Repository | BudgetRepository.cs |
| | Database | Migration | 001_CreateBudgetTables.cs |
| **Approvazioni Budget** | Frontend | Component | ApprovalWorkflow.tsx |
| | Frontend | Component | ApprovalHistory.tsx |
| | Frontend | Service | approvalApi.ts |
| | Backend | Command | ApproveBudgetCommand.cs |
| | Backend | Command | RejectBudgetCommand.cs |
| | Backend | Handler | ApproveBudgetHandler.cs |
| | Backend | Entity | BudgetApproval.cs |
| | Database | Migration | 002_CreateApprovalTables.cs |
| **Dashboard e Reporting** | Frontend | Component | Dashboard.tsx |
| | Frontend | Component | BudgetChart.tsx |
| | Frontend | Component | VarianceReport.tsx |
| | Frontend | Service | reportingApi.ts |
| | Backend | Query | GetDashboardDataQuery.cs |
| | Backend | Query | GetVarianceReportQuery.cs |
| | Backend | Handler | GetDashboardDataHandler.cs |
| **Autenticazione** | Frontend | Component | LoginForm.tsx |
| | Frontend | Hook | useAuth.ts |
| | Frontend | Service | authApi.ts |
| | Backend | Controller | AuthController.cs |
| | Backend | Service | AuthService.cs |
| | Backend | Entity | User.cs |
| **Configurazione Sistema** | Frontend | Component | SystemConfig.tsx |
| | Frontend | Service | configApi.ts |
| | Backend | Controller | ConfigController.cs |
| | Backend | Entity | SystemConfiguration.cs |

## 🏗️ Analisi Dettagliata per Layer

### Frontend Layer

#### Portal (Shell Application)
```
portal/
├── src/
│   ├── components/
│   │   ├── Layout/
│   │   │   ├── Header.tsx                    # Header globale con navigazione
│   │   │   ├── Sidebar.tsx                   # Menu laterale con moduli
│   │   │   └── MainLayout.tsx                # Layout principale applicazione
│   │   ├── Auth/
│   │   │   ├── LoginForm.tsx                 # Form di login
│   │   │   ├── ProtectedRoute.tsx            # Route protette
│   │   │   └── UserProfile.tsx               # Profilo utente
│   │   └── Common/
│   │       ├── LoadingSpinner.tsx            # Spinner di caricamento
│   │       ├── ErrorBoundary.tsx             # Gestione errori React
│   │       └── NotificationCenter.tsx        # Centro notifiche
│   ├── pages/
│   │   ├── Dashboard.tsx                     # Dashboard principale
│   │   ├── BudgetModule.tsx                  # Container per micro-frontend budget
│   │   └── NotFound.tsx                      # Pagina 404
│   ├── services/
│   │   ├── authApi.ts                        # API autenticazione
│   │   ├── moduleLoader.ts                   # Caricamento dinamico moduli
│   │   └── notificationService.ts            # Servizio notifiche
│   ├── hooks/
│   │   ├── useAuth.ts                        # Hook autenticazione
│   │   ├── useModuleFederation.ts            # Hook Module Federation
│   │   └── useNotifications.ts               # Hook notifiche
│   ├── store/
│   │   ├── authStore.ts                      # Store Zustand autenticazione
│   │   ├── globalStore.ts                    # Store globale applicazione
│   │   └── notificationStore.ts              # Store notifiche
│   ├── types/
│   │   ├── auth.ts                           # Tipi autenticazione
│   │   ├── user.ts                           # Tipi utente
│   │   └── common.ts                         # Tipi comuni
│   └── utils/
│       ├── constants.ts                      # Costanti applicazione
│       ├── formatters.ts                     # Utility formattazione
│       └── validators.ts                     # Validatori comuni
```

#### Budget Frontend (Micro-frontend)
```
budget-fe/
├── src/
│   ├── components/
│   │   ├── Budget/
│   │   │   ├── BudgetList.tsx                # Lista budget con filtri
│   │   │   ├── BudgetCard.tsx                # Card singolo budget
│   │   │   ├── CreateBudgetForm.tsx          # Form creazione budget
│   │   │   ├── EditBudgetForm.tsx            # Form modifica budget
│   │   │   ├── BudgetDetails.tsx             # Dettagli budget
│   │   │   └── BudgetSummary.tsx             # Riepilogo budget
│   │   ├── Categories/
│   │   │   ├── CategoryList.tsx              # Lista categorie budget
│   │   │   ├── CategoryForm.tsx              # Form categoria
│   │   │   └── CategoryChart.tsx             # Grafico distribuzione categorie
│   │   ├── Approvals/
│   │   │   ├── ApprovalWorkflow.tsx          # Workflow approvazioni
│   │   │   ├── ApprovalHistory.tsx           # Storico approvazioni
│   │   │   ├── ApprovalButton.tsx            # Pulsante approvazione
│   │   │   └── RejectionModal.tsx            # Modal rifiuto con motivo
│   │   ├── Reports/
│   │   │   ├── VarianceReport.tsx            # Report varianze
│   │   │   ├── BudgetChart.tsx               # Grafici budget
│   │   │   ├── ExportButton.tsx              # Export dati
│   │   │   └── FilterPanel.tsx               # Pannello filtri report
│   │   └── Common/
│   │       ├── MoneyInput.tsx                # Input per importi
│   │       ├── CurrencySelector.tsx          # Selettore valuta
│   │       └── DateRangePicker.tsx           # Selettore range date
│   ├── services/
│   │   ├── budgetApi.ts                      # API gestione budget
│   │   ├── approvalApi.ts                    # API approvazioni
│   │   ├── reportingApi.ts                   # API reporting
│   │   └── categoryApi.ts                    # API categorie
│   ├── hooks/
│   │   ├── useBudgets.ts                     # Hook gestione budget
│   │   ├── useApprovals.ts                   # Hook approvazioni
│   │   ├── useCategories.ts                  # Hook categorie
│   │   └── useReports.ts                     # Hook reporting
│   ├── store/
│   │   ├── budgetStore.ts                    # Store budget locale
│   │   └── filterStore.ts                    # Store filtri
│   ├── types/
│   │   ├── budget.ts                         # Tipi budget
│   │   ├── approval.ts                       # Tipi approvazioni
│   │   ├── category.ts                       # Tipi categorie
│   │   └── report.ts                         # Tipi report
│   └── utils/
│       ├── budgetCalculations.ts             # Calcoli budget
│       ├── currencyUtils.ts                  # Utility valute
│       └── chartHelpers.ts                   # Helper grafici
```

### Backend Layer

#### Budget API (.NET 9)
```
budget-api/
├── src/
│   ├── BudgetManagement.WebAPI/
│   │   ├── Controllers/
│   │   │   ├── BudgetController.cs           # Controller gestione budget
│   │   │   ├── ApprovalController.cs         # Controller approvazioni
│   │   │   ├── CategoryController.cs         # Controller categorie
│   │   │   ├── ReportController.cs           # Controller reporting
│   │   │   └── PlantController.cs            # Controller impianti
│   │   ├── Middleware/
│   │   │   ├── ExceptionMiddleware.cs        # Gestione eccezioni globali
│   │   │   ├── AuthenticationMiddleware.cs   # Middleware autenticazione
│   │   │   └── LoggingMiddleware.cs          # Middleware logging
│   │   ├── Filters/
│   │   │   ├── ValidationFilter.cs          # Filtro validazione
│   │   │   └── AuthorizationFilter.cs       # Filtro autorizzazione
│   │   └── Program.cs                        # Entry point applicazione
│   ├── BudgetManagement.Application/
│   │   ├── Commands/
│   │   │   ├── Budget/
│   │   │   │   ├── CreateBudgetCommand.cs    # Comando creazione budget
│   │   │   │   ├── UpdateBudgetCommand.cs    # Comando aggiornamento budget
│   │   │   │   ├── DeleteBudgetCommand.cs    # Comando eliminazione budget
│   │   │   │   ├── SubmitBudgetCommand.cs    # Comando invio approvazione
│   │   │   │   ├── ApproveBudgetCommand.cs   # Comando approvazione
│   │   │   │   └── RejectBudgetCommand.cs    # Comando rifiuto
│   │   │   └── Category/
│   │   │       ├── CreateCategoryCommand.cs  # Comando creazione categoria
│   │   │       └── UpdateCategoryCommand.cs  # Comando aggiornamento categoria
│   │   ├── Queries/
│   │   │   ├── Budget/
│   │   │   │   ├── GetBudgetsQuery.cs        # Query lista budget
│   │   │   │   ├── GetBudgetByIdQuery.cs     # Query budget per ID
│   │   │   │   ├── GetBudgetsByPlantQuery.cs # Query budget per impianto
│   │   │   │   └── GetBudgetsByYearQuery.cs  # Query budget per anno
│   │   │   ├── Reports/
│   │   │   │   ├── GetDashboardDataQuery.cs  # Query dati dashboard
│   │   │   │   ├── GetVarianceReportQuery.cs # Query report varianze
│   │   │   │   └── GetBudgetSummaryQuery.cs  # Query riepilogo budget
│   │   │   └── Approval/
│   │   │       ├── GetPendingApprovalsQuery.cs # Query approvazioni pending
│   │   │       └── GetApprovalHistoryQuery.cs  # Query storico approvazioni
│   │   ├── Handlers/
│   │   │   ├── Budget/
│   │   │   │   ├── CreateBudgetHandler.cs    # Handler creazione budget
│   │   │   │   ├── UpdateBudgetHandler.cs    # Handler aggiornamento budget
│   │   │   │   ├── GetBudgetsHandler.cs      # Handler query budget
│   │   │   │   ├── ApproveBudgetHandler.cs   # Handler approvazione
│   │   │   │   └── RejectBudgetHandler.cs    # Handler rifiuto
│   │   │   ├── Reports/
│   │   │   │   ├── GetDashboardDataHandler.cs # Handler dati dashboard
│   │   │   │   └── GetVarianceReportHandler.cs # Handler report varianze
│   │   │   └── Category/
│   │   │       ├── CreateCategoryHandler.cs  # Handler creazione categoria
│   │   │       └── GetCategoriesHandler.cs   # Handler query categorie
│   │   ├── DTOs/
│   │   │   ├── Budget/
│   │   │   │   ├── BudgetDto.cs              # DTO budget completo
│   │   │   │   ├── BudgetSummaryDto.cs       # DTO riepilogo budget
│   │   │   │   ├── CreateBudgetDto.cs        # DTO creazione budget
│   │   │   │   └── UpdateBudgetDto.cs        # DTO aggiornamento budget
│   │   │   ├── Category/
│   │   │   │   ├── CategoryDto.cs            # DTO categoria
│   │   │   │   └── CreateCategoryDto.cs      # DTO creazione categoria
│   │   │   ├── Approval/
│   │   │   │   ├── ApprovalDto.cs            # DTO approvazione
│   │   │   │   └── ApprovalHistoryDto.cs     # DTO storico approvazioni
│   │   │   └── Common/
│   │   │       ├── PagedResultDto.cs         # DTO risultati paginati
│   │   │       └── ApiResponseDto.cs         # DTO risposta API standard
│   │   ├── Validators/
│   │   │   ├── CreateBudgetValidator.cs      # Validatore creazione budget
│   │   │   ├── UpdateBudgetValidator.cs      # Validatore aggiornamento budget
│   │   │   └── CategoryValidator.cs          # Validatore categoria
│   │   ├── Mappings/
│   │   │   ├── BudgetMappingProfile.cs       # Profilo mapping budget
│   │   │   ├── CategoryMappingProfile.cs     # Profilo mapping categoria
│   │   │   └── ApprovalMappingProfile.cs     # Profilo mapping approvazioni
│   │   └── Interfaces/
│   │       ├── IBudgetService.cs             # Interfaccia servizio budget
│   │       ├── IApprovalService.cs           # Interfaccia servizio approvazioni
│   │       └── IReportingService.cs          # Interfaccia servizio reporting
│   ├── BudgetManagement.Domain/
│   │   ├── Entities/
│   │   │   ├── Budget.cs                     # Entità budget (Aggregate Root)
│   │   │   ├── BudgetCategory.cs             # Entità categoria budget
│   │   │   ├── BudgetApproval.cs             # Entità approvazione budget
│   │   │   ├── Plant.cs                      # Entità impianto
│   │   │   ├── User.cs                       # Entità utente
│   │   │   └── AuditableEntity.cs            # Entità base con audit
│   │   ├── ValueObjects/
│   │   │   ├── Money.cs                      # Value object denaro
│   │   │   ├── BudgetId.cs                   # Value object ID budget
│   │   │   ├── PlantId.cs                    # Value object ID impianto
│   │   │   └── UserId.cs                     # Value object ID utente
│   │   ├── Enums/
│   │   │   ├── BudgetStatus.cs               # Enum stato budget
│   │   │   ├── ApprovalStatus.cs             # Enum stato approvazione
│   │   │   ├── UserRole.cs                   # Enum ruolo utente
│   │   │   └── CategoryType.cs               # Enum tipo categoria
│   │   ├── Events/
│   │   │   ├── BudgetCreatedEvent.cs         # Evento budget creato
│   │   │   ├── BudgetApprovedEvent.cs        # Evento budget approvato
│   │   │   ├── BudgetRejectedEvent.cs        # Evento budget rifiutato
│   │   │   └── BudgetSubmittedEvent.cs       # Evento budget inviato
│   │   ├── Interfaces/
│   │   │   ├── IBudgetRepository.cs          # Interfaccia repository budget
│   │   │   ├── IPlantRepository.cs           # Interfaccia repository impianti
│   │   │   ├── IUserRepository.cs            # Interfaccia repository utenti
│   │   │   └── IUnitOfWork.cs                # Interfaccia Unit of Work
│   │   └── Specifications/
│   │       ├── BudgetSpecifications.cs       # Specifiche query budget
│   │       ├── ApprovalSpecifications.cs     # Specifiche query approvazioni
│   │       └── BaseSpecification.cs          # Specifica base
│   └── BudgetManagement.Infrastructure/
│       ├── Data/
│       │   ├── BudgetManagementContext.cs    # Context Entity Framework
│       │   ├── DbInitializer.cs              # Inizializzatore database
│       │   └── DesignTimeDbContextFactory.cs # Factory per migrations
│       ├── Configurations/
│       │   ├── BudgetConfiguration.cs        # Configurazione EF budget
│       │   ├── CategoryConfiguration.cs      # Configurazione EF categoria
│       │   ├── ApprovalConfiguration.cs      # Configurazione EF approvazione
│       │   ├── PlantConfiguration.cs         # Configurazione EF impianto
│       │   └── UserConfiguration.cs          # Configurazione EF utente
│       ├── Repositories/
│       │   ├── BudgetRepository.cs           # Repository budget
│       │   ├── PlantRepository.cs            # Repository impianti
│       │   ├── UserRepository.cs             # Repository utenti
│       │   ├── GenericRepository.cs          # Repository generico
│       │   └── UnitOfWork.cs                 # Implementazione Unit of Work
│       ├── Services/
│       │   ├── EmailService.cs               # Servizio email
│       │   ├── NotificationService.cs        # Servizio notifiche
│       │   ├── AuditService.cs               # Servizio audit
│       │   └── CacheService.cs               # Servizio cache Redis
│       └── Migrations/
│           ├── 001_InitialCreate.cs          # Migration iniziale
│           ├── 002_AddApprovalTables.cs      # Migration tabelle approvazioni
│           ├── 003_AddAuditFields.cs         # Migration campi audit
│           └── 004_AddIndexes.cs             # Migration indici performance
```

### Database Layer

#### PostgreSQL Schema
```sql
-- Tabelle principali del sistema Budget Management

-- Tabella Plants (Impianti)
CREATE TABLE Plants (
    Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    Name VARCHAR(255) NOT NULL,
    Code VARCHAR(50) NOT NULL UNIQUE,
    Location VARCHAR(255),
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabella Users (Utenti)
CREATE TABLE Users (
    Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    Email VARCHAR(255) NOT NULL UNIQUE,
    FirstName VARCHAR(100) NOT NULL,
    LastName VARCHAR(100) NOT NULL,
    Role VARCHAR(50) NOT NULL,
    PlantId UUID REFERENCES Plants(Id),
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabella Budgets (Budget)
CREATE TABLE Budgets (
    Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    PlantId UUID NOT NULL REFERENCES Plants(Id),
    Year INTEGER NOT NULL,
    TotalAmount DECIMAL(18,2) NOT NULL,
    Currency VARCHAR(3) NOT NULL DEFAULT 'EUR',
    Status VARCHAR(20) NOT NULL DEFAULT 'Draft',
    Notes TEXT,
    CreatedBy UUID NOT NULL REFERENCES Users(Id),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    SubmittedAt TIMESTAMP,
    ApprovedAt TIMESTAMP,
    UNIQUE(PlantId, Year)
);

-- Tabella BudgetCategories (Categorie Budget)
CREATE TABLE BudgetCategories (
    Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    BudgetId UUID NOT NULL REFERENCES Budgets(Id) ON DELETE CASCADE,
    CategoryName VARCHAR(255) NOT NULL,
    CategoryCode VARCHAR(50) NOT NULL,
    Description TEXT,
    TotalAmount DECIMAL(18,2) NOT NULL,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabella BudgetCategoryMonths (Distribuzione Mensile)
CREATE TABLE BudgetCategoryMonths (
    Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    CategoryId UUID NOT NULL REFERENCES BudgetCategories(Id) ON DELETE CASCADE,
    Month INTEGER NOT NULL CHECK (Month >= 1 AND Month <= 12),
    Amount DECIMAL(18,2) NOT NULL,
    UNIQUE(CategoryId, Month)
);

-- Tabella BudgetApprovals (Approvazioni)
CREATE TABLE BudgetApprovals (
    Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    BudgetId UUID NOT NULL REFERENCES Budgets(Id) ON DELETE CASCADE,
    ApproverUserId UUID NOT NULL REFERENCES Users(Id),
    ApprovalLevel INTEGER NOT NULL,
    Status VARCHAR(20) NOT NULL DEFAULT 'Pending',
    Notes TEXT,
    ApprovedAt TIMESTAMP,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabella AuditLogs (Log Audit)
CREATE TABLE AuditLogs (
    Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    EntityType VARCHAR(100) NOT NULL,
    EntityId UUID NOT NULL,
    Action VARCHAR(50) NOT NULL,
    UserId UUID REFERENCES Users(Id),
    OldValues JSONB,
    NewValues JSONB,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indici per performance
CREATE INDEX idx_budgets_plant_year ON Budgets(PlantId, Year);
CREATE INDEX idx_budgets_status ON Budgets(Status);
CREATE INDEX idx_budgets_created_at ON Budgets(CreatedAt);
CREATE INDEX idx_budget_categories_budget_id ON BudgetCategories(BudgetId);
CREATE INDEX idx_budget_approvals_budget_id ON BudgetApprovals(BudgetId);
CREATE INDEX idx_budget_approvals_approver ON BudgetApprovals(ApproverUserId);
CREATE INDEX idx_audit_logs_entity ON AuditLogs(EntityType, EntityId);
CREATE INDEX idx_audit_logs_created_at ON AuditLogs(CreatedAt);
```

## 🔄 Flusso Dati End-to-End

### Esempio: Creazione Budget

1. **Frontend (budget-fe/CreateBudgetForm.tsx)**
   - Utente compila form creazione budget
   - Validazione client-side con React Hook Form + Zod
   - Chiamata API tramite budgetApi.ts

2. **API Layer (BudgetController.cs)**
   - Riceve richiesta HTTP POST /api/v1/budget
   - Validazione DTO con FluentValidation
   - Invio comando tramite MediatR

3. **Application Layer (CreateBudgetHandler.cs)**
   - Elaborazione business logic
   - Validazione regole di dominio
   - Creazione entità Budget

4. **Domain Layer (Budget.cs)**
   - Applicazione regole di business
   - Generazione eventi di dominio
   - Validazione invarianti

5. **Infrastructure Layer (BudgetRepository.cs)**
   - Persistenza su PostgreSQL
   - Gestione transazioni
   - Cache su Redis

6. **Response Flow**
   - Ritorno DTO al frontend
   - Aggiornamento stato React Query
   - Refresh UI automatico

## 📈 Metriche e KPI Tecnici

### Performance Metrics
- **API Response Time**: < 200ms per 95% delle richieste
- **Frontend Load Time**: < 2s per first contentful paint
- **Database Query Time**: < 50ms per query semplici
- **Cache Hit Ratio**: > 80% per dati frequenti

### Quality Metrics
- **Code Coverage**: > 80% per backend, > 70% per frontend
- **Technical Debt**: < 5% del tempo di sviluppo
- **Bug Density**: < 1 bug per 1000 righe di codice
- **Security Vulnerabilities**: 0 critiche, < 5 medie

### Scalability Metrics
- **Concurrent Users**: Supporto per 1000+ utenti simultanei
- **Data Volume**: Gestione di 100k+ budget records
- **API Throughput**: 1000+ richieste/secondo
- **Database Connections**: Pool di 50-100 connessioni
