# Budget Management System - Guida al Deployment

## 🚀 Panoramica Deployment

Questa guida fornisce istruzioni complete per il deployment del Budget Management System in diversi ambienti, dalla configurazione locale alla produzione.

## 🏗️ Architettura di Deployment

### Ambienti Supportati

1. **Development** - Ambiente locale sviluppatori
2. **Testing** - Ambiente per test automatizzati
3. **Staging** - Ambiente pre-produzione
4. **Production** - Ambiente produzione

### Stack Tecnologico Deployment

- **Container Runtime**: Podman/Docker
- **Orchestrazione**: Docker Compose / Kubernetes
- **Reverse Proxy**: NGINX
- **Database**: PostgreSQL 16
- **Cache**: Redis 7
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack

## 🔧 Prerequisiti

### Software Richiesto

```bash
# Container Runtime
podman --version  # >= 4.0
# oppure
docker --version  # >= 24.0

# Node.js per frontend
node --version    # >= 20.0
npm --version     # >= 10.0

# .NET per backend
dotnet --version  # >= 9.0

# Database (opzionale se si usa container)
psql --version    # >= 16.0
```

### Configurazione Sistema

```bash
# Aumenta limiti file per PostgreSQL
echo "fs.file-max = 65536" >> /etc/sysctl.conf

# Configurazione memoria per Redis
echo "vm.overcommit_memory = 1" >> /etc/sysctl.conf

# Ricarica configurazione
sysctl -p
```

## 🏠 Deployment Locale (Development)

### 1. Clone Repository
```bash
git clone <repository-url>
cd pianificazione_tecnica
```

### 2. Configurazione Environment
```bash
# Copia template configurazione
cp config.env.template config.env

# Modifica configurazione per ambiente locale
nano config.env
```

**config.env per Development:**
```bash
# Database Configuration
POSTGRES_DB=budget_management_dev
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# API Configuration
BUDGET_API_URL=http://localhost:5000
AUTH_API_URL=http://localhost:5001

# Frontend Configuration
PORTAL_URL=http://localhost:3000
BUDGET_FE_URL=http://localhost:3001

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-for-development
JWT_ISSUER=budget-management-dev
JWT_AUDIENCE=budget-users

# Logging
LOG_LEVEL=Debug
```

### 3. Avvio Servizi Infrastruttura
```bash
# Avvia PostgreSQL e Redis
podman-compose up -d postgres redis

# Verifica stato servizi
podman-compose ps
```

### 4. Setup Database
```bash
# Esegui migrazioni database
cd budget-api
dotnet ef database update --project src/BudgetManagement.Infrastructure

# Seed dati iniziali (opzionale)
dotnet run --project src/BudgetManagement.WebAPI -- --seed-data
```

### 5. Avvio Frontend
```bash
# Terminal 1 - Budget Frontend
cd budget-fe
npm install
npm run dev

# Terminal 2 - Portal
cd ../portal
npm install
npm run dev
```

### 6. Avvio Backend (Opzionale)
```bash
# Se non si usa container per API
cd budget-api
dotnet run --project src/BudgetManagement.WebAPI
```

### 7. Verifica Deployment
```bash
# Verifica servizi attivi
curl http://localhost:3000  # Portal
curl http://localhost:3001  # Budget FE
curl http://localhost:5000/health  # Budget API
```

## 🧪 Deployment Testing

### Configurazione Testing Environment

**docker-compose.test.yml:**
```yaml
version: '3.8'

services:
  postgres-test:
    image: postgres:16-alpine
    environment:
      - POSTGRES_DB=budget_management_test
      - POSTGRES_USER=test_user
      - POSTGRES_PASSWORD=test_password
    ports:
      - "5433:5432"
    tmpfs:
      - /var/lib/postgresql/data

  redis-test:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    tmpfs:
      - /data

  budget-api-test:
    build:
      context: ./budget-api
      dockerfile: Dockerfile.test
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
      - ConnectionStrings__DefaultConnection=Host=postgres-test;Database=budget_management_test;Username=test_user;Password=test_password
    depends_on:
      - postgres-test
      - redis-test
    command: ["dotnet", "test", "--logger", "trx"]
```

### Esecuzione Test
```bash
# Test backend
cd budget-api
dotnet test --configuration Release --logger trx --results-directory ./TestResults

# Test frontend
cd budget-fe
npm run test:ci

cd ../portal
npm run test:ci

# Test integrazione
podman-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 🎭 Deployment Staging

### 1. Configurazione Staging

**config.staging.env:**
```bash
# Database Configuration
POSTGRES_DB=budget_management_staging
POSTGRES_USER=budget_staging
POSTGRES_PASSWORD=${STAGING_DB_PASSWORD}
POSTGRES_HOST=staging-postgres.internal
POSTGRES_PORT=5432

# Redis Configuration
REDIS_HOST=staging-redis.internal
REDIS_PORT=6379

# API Configuration
BUDGET_API_URL=https://staging-api.budget.company.com
AUTH_API_URL=https://staging-auth.budget.company.com

# Frontend Configuration
PORTAL_URL=https://staging.budget.company.com
BUDGET_FE_URL=https://staging-budget.budget.company.com

# JWT Configuration
JWT_SECRET=${STAGING_JWT_SECRET}
JWT_ISSUER=budget-management-staging
JWT_AUDIENCE=budget-users

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/staging.crt
SSL_KEY_PATH=/etc/ssl/private/staging.key

# Logging
LOG_LEVEL=Information
```

### 2. Docker Compose Staging

**docker-compose.staging.yml:**
```yaml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/staging.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
    depends_on:
      - portal
      - budget-fe
      - budget-api

  portal:
    build:
      context: ./portal
      dockerfile: Dockerfile.prod
    environment:
      - NODE_ENV=staging
      - VITE_BUDGET_FE_URL=${BUDGET_FE_URL}
      - VITE_BUDGET_API_URL=${BUDGET_API_URL}

  budget-fe:
    build:
      context: ./budget-fe
      dockerfile: Dockerfile.prod
    environment:
      - NODE_ENV=staging
      - VITE_API_URL=${BUDGET_API_URL}

  budget-api:
    build:
      context: ./budget-api
      dockerfile: Dockerfile
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=${DATABASE_CONNECTION_STRING}
      - ConnectionStrings__Redis=${REDIS_CONNECTION_STRING}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  default:
    external:
      name: staging-network
```

### 3. Deploy Staging
```bash
# Build e deploy
docker-compose -f docker-compose.staging.yml build
docker-compose -f docker-compose.staging.yml up -d

# Verifica deployment
curl -k https://staging.budget.company.com/health
```

## 🏭 Deployment Produzione

### 1. Configurazione Produzione

**config.production.env:**
```bash
# Database Configuration (Azure PostgreSQL / AWS RDS)
POSTGRES_DB=budget_management_prod
POSTGRES_USER=budget_prod
POSTGRES_PASSWORD=${PROD_DB_PASSWORD}
POSTGRES_HOST=prod-postgres.database.azure.com
POSTGRES_PORT=5432
POSTGRES_SSL_MODE=require

# Redis Configuration (Azure Cache / AWS ElastiCache)
REDIS_HOST=prod-redis.redis.cache.windows.net
REDIS_PORT=6380
REDIS_SSL=true
REDIS_PASSWORD=${PROD_REDIS_PASSWORD}

# API Configuration
BUDGET_API_URL=https://api.budget.company.com
AUTH_API_URL=https://auth.budget.company.com

# Frontend Configuration
PORTAL_URL=https://budget.company.com
BUDGET_FE_URL=https://budget-fe.budget.company.com

# JWT Configuration
JWT_SECRET=${PROD_JWT_SECRET}
JWT_ISSUER=budget-management-prod
JWT_AUDIENCE=budget-users

# Azure AD / OAuth Configuration
AZURE_AD_TENANT_ID=${AZURE_TENANT_ID}
AZURE_AD_CLIENT_ID=${AZURE_CLIENT_ID}
AZURE_AD_CLIENT_SECRET=${AZURE_CLIENT_SECRET}

# Monitoring
APPLICATION_INSIGHTS_KEY=${APP_INSIGHTS_KEY}
PROMETHEUS_ENDPOINT=https://prometheus.monitoring.company.com

# Logging
LOG_LEVEL=Warning
SERILOG_MINIMUM_LEVEL=Warning
```

### 2. Kubernetes Deployment

**k8s/namespace.yaml:**
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: budget-management
```

**k8s/configmap.yaml:**
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: budget-config
  namespace: budget-management
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  JWT_ISSUER: "budget-management-prod"
  JWT_AUDIENCE: "budget-users"
  LOG_LEVEL: "Warning"
```

**k8s/secret.yaml:**
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: budget-secrets
  namespace: budget-management
type: Opaque
data:
  database-connection: <base64-encoded-connection-string>
  redis-connection: <base64-encoded-redis-connection>
  jwt-secret: <base64-encoded-jwt-secret>
```

**k8s/deployment.yaml:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: budget-api
  namespace: budget-management
spec:
  replicas: 3
  selector:
    matchLabels:
      app: budget-api
  template:
    metadata:
      labels:
        app: budget-api
    spec:
      containers:
      - name: budget-api
        image: budget-management/budget-api:latest
        ports:
        - containerPort: 8080
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: budget-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: budget-secrets
              key: database-connection
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

**k8s/service.yaml:**
```yaml
apiVersion: v1
kind: Service
metadata:
  name: budget-api-service
  namespace: budget-management
spec:
  selector:
    app: budget-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
```

**k8s/ingress.yaml:**
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: budget-ingress
  namespace: budget-management
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - budget.company.com
    - api.budget.company.com
    secretName: budget-tls
  rules:
  - host: budget.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: portal-service
            port:
              number: 80
  - host: api.budget.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: budget-api-service
            port:
              number: 80
```

### 3. Deploy Produzione
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
kubectl apply -f k8s/ingress.yaml

# Verifica deployment
kubectl get pods -n budget-management
kubectl get services -n budget-management
kubectl get ingress -n budget-management

# Verifica health
curl https://api.budget.company.com/health
```

## 📊 Monitoring e Logging

### Prometheus Configuration

**prometheus.yml:**
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'budget-api'
    static_configs:
      - targets: ['budget-api:8080']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

### Grafana Dashboard

**dashboard.json:** (Estratto)
```json
{
  "dashboard": {
    "title": "Budget Management System",
    "panels": [
      {
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Database Connections",
        "type": "singlestat",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends{datname=\"budget_management_prod\"}",
            "legendFormat": "Active Connections"
          }
        ]
      }
    ]
  }
}
```

## 🔒 Security Checklist

### Pre-Deployment Security

- [ ] **Secrets Management**: Tutti i segreti sono gestiti tramite Azure Key Vault / AWS Secrets Manager
- [ ] **SSL/TLS**: Certificati SSL configurati e validi
- [ ] **Database Security**: Connessioni crittografate, utenti con privilegi minimi
- [ ] **API Security**: Rate limiting, CORS configurato, JWT validation
- [ ] **Container Security**: Immagini scansionate per vulnerabilità
- [ ] **Network Security**: Firewall configurato, porte non necessarie chiuse
- [ ] **Backup Strategy**: Backup automatici database configurati
- [ ] **Monitoring**: Alert configurati per eventi di sicurezza

### Post-Deployment Verification

```bash
# Test SSL
curl -I https://budget.company.com

# Test API Security
curl -H "Authorization: Bearer invalid-token" https://api.budget.company.com/api/v1/budget

# Test Database Connection
psql "********************************/db?sslmode=require"

# Verifica backup
pg_dump --host=prod-host --username=backup-user budget_management_prod > backup_test.sql
```

## 🚨 Troubleshooting

### Problemi Comuni

#### 1. Container non si avvia
```bash
# Verifica log
podman logs budget-api

# Verifica configurazione
podman inspect budget-api

# Test connettività database
podman exec budget-api pg_isready -h postgres -U postgres
```

#### 2. Frontend non carica
```bash
# Verifica build
npm run build

# Verifica variabili ambiente
echo $VITE_API_URL

# Test connettività API
curl $VITE_API_URL/health
```

#### 3. Database connection issues
```bash
# Test connessione diretta
psql -h localhost -U postgres -d budget_management

# Verifica pool connessioni
SELECT count(*) FROM pg_stat_activity WHERE datname = 'budget_management';

# Verifica log PostgreSQL
tail -f /var/log/postgresql/postgresql.log
```

### Recovery Procedures

#### Rollback Deployment
```bash
# Kubernetes rollback
kubectl rollout undo deployment/budget-api -n budget-management

# Docker Compose rollback
docker-compose down
docker-compose up -d --scale budget-api=0
docker-compose up -d
```

#### Database Recovery
```bash
# Restore da backup
pg_restore -h prod-host -U postgres -d budget_management backup_file.dump

# Point-in-time recovery
pg_basebackup -h prod-host -D /backup/base -U replication -v -P -W
```

## 📋 Checklist Deployment

### Pre-Deployment
- [ ] Codice testato e approvato
- [ ] Configurazioni ambiente verificate
- [ ] Backup database eseguito
- [ ] Certificati SSL validi
- [ ] Monitoring configurato
- [ ] Alert configurati
- [ ] Piano di rollback preparato

### Durante Deployment
- [ ] Servizi fermati in ordine corretto
- [ ] Database migrato
- [ ] Nuove versioni deployate
- [ ] Health check passati
- [ ] Smoke test eseguiti

### Post-Deployment
- [ ] Tutti i servizi funzionanti
- [ ] Performance baseline verificata
- [ ] Log monitoring attivo
- [ ] Backup post-deployment
- [ ] Documentazione aggiornata
- [ ] Team notificato
