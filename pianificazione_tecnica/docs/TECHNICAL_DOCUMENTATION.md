# Budget Management System - Documentazione Tecnica

## 🏗️ Architettura Tecnica

### Architettura Generale

Il sistema segue un'architettura **micro-frontend** con backend distribuito, implementando i principi di **Domain-Driven Design** e **Clean Architecture**.

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                      │
├─────────────────────────────────────────────────────────────┤
│  Portal (Shell)     │  Budget FE        │  Future Modules   │
│  React 18 + TS      │  React 18 + TS    │  React 18 + TS    │
│  Module Federation  │  Ant Design       │  TBD              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼ HTTP/REST
┌─────────────────────────────────────────────────────────────┐
│                    APPLICATION LAYER                       │
├─────────────────────────────────────────────────────────────┤
│  Budget API         │  Auth API         │  Future APIs      │
│  .NET 9             │  .NET 9           │  .NET 9           │
│  Clean Architecture │  Identity Server  │  TBD              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼ Entity Framework
┌─────────────────────────────────────────────────────────────┐
│                    PERSISTENCE LAYER                       │
├─────────────────────────────────────────────────────────────┤
│  PostgreSQL 16      │  Redis 7          │  File Storage     │
│  Primary Database   │  Cache & Sessions │  Documents        │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 Frontend Architecture

### Micro-Frontend Pattern

#### Module Federation Configuration

**Portal (Shell) - vite.config.ts**
```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import federation from '@originjs/vite-plugin-federation';

export default defineConfig({
  plugins: [
    react(),
    federation({
      name: 'portal',
      remotes: {
        'budget-fe': 'http://localhost:3001/assets/remoteEntry.js',
      },
      shared: ['react', 'react-dom', 'antd']
    })
  ],
  build: {
    target: 'esnext',
    minify: false,
    cssCodeSplit: false
  }
});
```

**Budget Frontend - vite.config.ts**
```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import federation from '@originjs/vite-plugin-federation';

export default defineConfig({
  plugins: [
    react(),
    federation({
      name: 'budget-fe',
      filename: 'remoteEntry.js',
      exposes: {
        './BudgetApp': './src/App',
        './BudgetList': './src/components/BudgetList',
        './CreateBudgetForm': './src/components/CreateBudgetForm'
      },
      shared: ['react', 'react-dom', 'antd']
    })
  ],
  build: {
    target: 'esnext',
    minify: false,
    cssCodeSplit: false
  }
});
```

### State Management

#### Global State (Portal)
```typescript
// Zustand store per stato globale
interface GlobalState {
  user: User | null;
  theme: 'light' | 'dark';
  language: string;
  notifications: Notification[];
}

const useGlobalStore = create<GlobalState>((set) => ({
  user: null,
  theme: 'light',
  language: 'it',
  notifications: [],
  setUser: (user) => set({ user }),
  setTheme: (theme) => set({ theme }),
  addNotification: (notification) => 
    set((state) => ({ 
      notifications: [...state.notifications, notification] 
    }))
}));
```

#### Local State (Budget Module)
```typescript
// React Query per API calls
const useBudgets = () => {
  return useQuery({
    queryKey: ['budgets'],
    queryFn: () => budgetApi.getBudgets(),
    staleTime: 5 * 60 * 1000, // 5 minuti
  });
};

// React Hook Form per form management
const CreateBudgetForm = () => {
  const { control, handleSubmit, formState: { errors } } = useForm<CreateBudgetRequest>({
    resolver: zodResolver(createBudgetSchema)
  });
};
```

### Component Architecture

#### Shared Components Structure
```
shared/
├── components/
│   ├── Layout/
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   └── Footer.tsx
│   ├── Forms/
│   │   ├── FormField.tsx
│   │   ├── DatePicker.tsx
│   │   └── NumberInput.tsx
│   └── Charts/
│       ├── LineChart.tsx
│       ├── BarChart.tsx
│       └── PieChart.tsx
├── hooks/
│   ├── useAuth.ts
│   ├── useApi.ts
│   └── useLocalStorage.ts
├── utils/
│   ├── formatters.ts
│   ├── validators.ts
│   └── constants.ts
└── types/
    ├── api.ts
    ├── user.ts
    └── common.ts
```

## 🔧 Backend Architecture

### Clean Architecture Implementation

```
BudgetManagement.WebAPI/          # Presentation Layer
├── Controllers/                  # API Controllers
├── Middleware/                   # Custom middleware
├── Services/                     # Application services
└── Program.cs                    # Application entry point

BudgetManagement.Application/     # Application Layer
├── Commands/                     # CQRS Commands
├── Queries/                      # CQRS Queries
├── Handlers/                     # Command/Query handlers
├── DTOs/                         # Data Transfer Objects
├── Interfaces/                   # Application interfaces
├── Validators/                   # FluentValidation validators
└── Mappings/                     # AutoMapper profiles

BudgetManagement.Domain/          # Domain Layer
├── Entities/                     # Domain entities
├── ValueObjects/                 # Value objects
├── Enums/                        # Domain enums
├── Events/                       # Domain events
├── Interfaces/                   # Domain interfaces
└── Specifications/               # Domain specifications

BudgetManagement.Infrastructure/  # Infrastructure Layer
├── Data/                         # EF Core context
├── Repositories/                 # Repository implementations
├── Services/                     # External services
├── Configurations/               # EF configurations
└── Migrations/                   # Database migrations
```

### Domain Model

#### Core Entities
```csharp
// Budget Aggregate Root
public class Budget : AggregateRoot
{
    public BudgetId Id { get; private set; }
    public PlantId PlantId { get; private set; }
    public int Year { get; private set; }
    public Money TotalAmount { get; private set; }
    public BudgetStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ApprovedAt { get; private set; }
    
    private readonly List<BudgetCategory> _categories = new();
    public IReadOnlyList<BudgetCategory> Categories => _categories.AsReadOnly();
    
    private readonly List<BudgetApproval> _approvals = new();
    public IReadOnlyList<BudgetApproval> Approvals => _approvals.AsReadOnly();
    
    // Domain methods
    public void Submit() { /* Business logic */ }
    public void Approve(UserId approver, string notes) { /* Business logic */ }
    public void Reject(UserId approver, string reason) { /* Business logic */ }
}

// Value Objects
public record Money(decimal Amount, string Currency)
{
    public static Money Zero(string currency) => new(0, currency);
    public Money Add(Money other) => 
        Currency == other.Currency 
            ? new(Amount + other.Amount, Currency)
            : throw new InvalidOperationException("Cannot add different currencies");
}

public record BudgetId(Guid Value)
{
    public static BudgetId New() => new(Guid.NewGuid());
    public static implicit operator Guid(BudgetId id) => id.Value;
}
```

### CQRS Implementation

#### Commands
```csharp
// Create Budget Command
public record CreateBudgetCommand(
    Guid PlantId,
    int Year,
    decimal TotalAmount,
    string Currency,
    List<CreateBudgetCategoryDto> Categories
) : IRequest<BudgetDto>;

// Command Handler
public class CreateBudgetHandler : IRequestHandler<CreateBudgetCommand, BudgetDto>
{
    private readonly IBudgetRepository _repository;
    private readonly IMapper _mapper;
    
    public async Task<BudgetDto> Handle(CreateBudgetCommand request, CancellationToken cancellationToken)
    {
        // Validation
        var plant = await _plantRepository.GetByIdAsync(new PlantId(request.PlantId));
        if (plant == null) throw new NotFoundException("Plant not found");
        
        // Create domain entity
        var budget = Budget.Create(
            new PlantId(request.PlantId),
            request.Year,
            new Money(request.TotalAmount, request.Currency)
        );
        
        // Add categories
        foreach (var categoryDto in request.Categories)
        {
            budget.AddCategory(/* ... */);
        }
        
        // Persist
        await _repository.AddAsync(budget);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return _mapper.Map<BudgetDto>(budget);
    }
}
```

#### Queries
```csharp
// Get Budgets Query
public record GetBudgetsQuery(
    Guid? PlantId = null,
    int? Year = null,
    BudgetStatus? Status = null,
    int Page = 1,
    int PageSize = 20
) : IRequest<PagedResult<BudgetSummaryDto>>;

// Query Handler
public class GetBudgetsHandler : IRequestHandler<GetBudgetsQuery, PagedResult<BudgetSummaryDto>>
{
    private readonly IReadOnlyRepository<Budget> _repository;
    
    public async Task<PagedResult<BudgetSummaryDto>> Handle(GetBudgetsQuery request, CancellationToken cancellationToken)
    {
        var spec = new BudgetFilterSpecification(request.PlantId, request.Year, request.Status);
        
        var budgets = await _repository.ListAsync(spec, cancellationToken);
        var totalCount = await _repository.CountAsync(spec, cancellationToken);
        
        var dtos = budgets.Select(b => new BudgetSummaryDto
        {
            Id = b.Id,
            PlantName = b.Plant.Name,
            Year = b.Year,
            TotalAmount = b.TotalAmount.Amount,
            Currency = b.TotalAmount.Currency,
            Status = b.Status.ToString()
        }).ToList();
        
        return new PagedResult<BudgetSummaryDto>(dtos, totalCount, request.Page, request.PageSize);
    }
}
```

### Database Design

#### Entity Framework Configuration
```csharp
public class BudgetConfiguration : IEntityTypeConfiguration<Budget>
{
    public void Configure(EntityTypeBuilder<Budget> builder)
    {
        builder.ToTable("Budgets");
        
        builder.HasKey(b => b.Id);
        builder.Property(b => b.Id)
            .HasConversion(id => id.Value, value => new BudgetId(value));
        
        builder.OwnsOne(b => b.TotalAmount, money =>
        {
            money.Property(m => m.Amount).HasColumnName("TotalAmount").HasPrecision(18, 2);
            money.Property(m => m.Currency).HasColumnName("Currency").HasMaxLength(3);
        });
        
        builder.Property(b => b.Status)
            .HasConversion<string>()
            .HasMaxLength(20);
        
        builder.HasMany(b => b.Categories)
            .WithOne()
            .HasForeignKey("BudgetId")
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.HasMany(b => b.Approvals)
            .WithOne()
            .HasForeignKey("BudgetId")
            .OnDelete(DeleteBehavior.Cascade);
    }
}
```

## 🔄 API Design

### RESTful API Endpoints

```csharp
[ApiController]
[Route("api/v1/[controller]")]
public class BudgetController : ControllerBase
{
    private readonly IMediator _mediator;
    
    /// <summary>
    /// Get all budgets with optional filtering
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(PagedResult<BudgetSummaryDto>), 200)]
    public async Task<IActionResult> GetBudgets([FromQuery] GetBudgetsQuery query)
    {
        var result = await _mediator.Send(query);
        return Ok(result);
    }
    
    /// <summary>
    /// Get budget by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(BudgetDto), 200)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> GetBudget(Guid id)
    {
        var query = new GetBudgetByIdQuery(id);
        var result = await _mediator.Send(query);
        return Ok(result);
    }
    
    /// <summary>
    /// Create new budget
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(BudgetDto), 201)]
    [ProducesResponseType(typeof(ValidationProblemDetails), 400)]
    public async Task<IActionResult> CreateBudget([FromBody] CreateBudgetCommand command)
    {
        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetBudget), new { id = result.Id }, result);
    }
    
    /// <summary>
    /// Submit budget for approval
    /// </summary>
    [HttpPost("{id:guid}/submit")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> SubmitBudget(Guid id)
    {
        var command = new SubmitBudgetCommand(id);
        await _mediator.Send(command);
        return NoContent();
    }
}
```

### API Documentation (OpenAPI/Swagger)
```csharp
// Program.cs
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Budget Management API",
        Version = "v1",
        Description = "API for managing organizational budgets",
        Contact = new OpenApiContact
        {
            Name = "Development Team",
            Email = "<EMAIL>"
        }
    });
    
    // Include XML comments
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    c.IncludeXmlComments(xmlPath);
    
    // JWT Authentication
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
});
```

## 🐳 Containerization

### Docker Configuration

**Budget API Dockerfile**
```dockerfile
# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy csproj files and restore dependencies
COPY ["src/BudgetManagement.WebAPI/BudgetManagement.WebAPI.csproj", "src/BudgetManagement.WebAPI/"]
COPY ["src/BudgetManagement.Application/BudgetManagement.Application.csproj", "src/BudgetManagement.Application/"]
COPY ["src/BudgetManagement.Domain/BudgetManagement.Domain.csproj", "src/BudgetManagement.Domain/"]
COPY ["src/BudgetManagement.Infrastructure/BudgetManagement.Infrastructure.csproj", "src/BudgetManagement.Infrastructure/"]

RUN dotnet restore "src/BudgetManagement.WebAPI/BudgetManagement.WebAPI.csproj"

# Copy everything else and build
COPY . .
WORKDIR "/src/src/BudgetManagement.WebAPI"
RUN dotnet build "BudgetManagement.WebAPI.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "BudgetManagement.WebAPI.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

COPY --from=publish /app/publish .

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

EXPOSE 8080

ENTRYPOINT ["dotnet", "BudgetManagement.WebAPI.dll"]
```

### Docker Compose Configuration
```yaml
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: budget-postgres
    environment:
      - POSTGRES_DB=budget_management
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - budget-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: budget-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - budget-network

  # Budget API
  budget-api:
    build:
      context: ./budget-api
      dockerfile: Dockerfile
    container_name: budget-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=budget_management;Username=postgres;Password=postgres
      - ConnectionStrings__Redis=redis:6379
    ports:
      - "5000:8080"
    depends_on:
      - postgres
      - redis
    networks:
      - budget-network

volumes:
  postgres_data:
  redis_data:

networks:
  budget-network:
    driver: bridge
```

## 🔒 Security Implementation

### Authentication & Authorization
```csharp
// JWT Configuration
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:SecretKey"]))
        };
    });

// Authorization Policies
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("BudgetManager", policy =>
        policy.RequireRole("BudgetManager", "Admin"));
    
    options.AddPolicy("PlantManager", policy =>
        policy.RequireRole("PlantManager", "BudgetManager", "Admin"));
    
    options.AddPolicy("FinanceController", policy =>
        policy.RequireRole("FinanceController", "Admin"));
});
```

### Data Protection
```csharp
// Sensitive data encryption
public class EncryptionService : IEncryptionService
{
    private readonly IDataProtector _protector;
    
    public EncryptionService(IDataProtectionProvider provider)
    {
        _protector = provider.CreateProtector("BudgetManagement.SensitiveData");
    }
    
    public string Encrypt(string plainText) => _protector.Protect(plainText);
    public string Decrypt(string cipherText) => _protector.Unprotect(cipherText);
}
```

## 📊 Performance & Monitoring

### Caching Strategy
```csharp
// Redis caching implementation
public class CachedBudgetRepository : IBudgetRepository
{
    private readonly IBudgetRepository _repository;
    private readonly IDistributedCache _cache;
    private readonly TimeSpan _cacheDuration = TimeSpan.FromMinutes(15);
    
    public async Task<Budget?> GetByIdAsync(BudgetId id)
    {
        var cacheKey = $"budget:{id}";
        var cached = await _cache.GetStringAsync(cacheKey);
        
        if (cached != null)
        {
            return JsonSerializer.Deserialize<Budget>(cached);
        }
        
        var budget = await _repository.GetByIdAsync(id);
        if (budget != null)
        {
            var serialized = JsonSerializer.Serialize(budget);
            await _cache.SetStringAsync(cacheKey, serialized, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _cacheDuration
            });
        }
        
        return budget;
    }
}
```

### Health Checks
```csharp
// Health checks configuration
builder.Services.AddHealthChecks()
    .AddNpgSql(connectionString, name: "postgres")
    .AddRedis(redisConnectionString, name: "redis")
    .AddCheck<BudgetApiHealthCheck>("budget-api");

app.MapHealthChecks("/health", new HealthCheckOptions
{
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});
```

## 🚀 Deployment & DevOps

### CI/CD Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy Budget Management System

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
      - name: Install dependencies
        run: |
          cd budget-fe && npm ci
          cd ../portal && npm ci
      - name: Run tests
        run: |
          cd budget-fe && npm run test
          cd ../portal && npm run test
      - name: Build
        run: |
          cd budget-fe && npm run build
          cd ../portal && npm run build

  test-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '9.0.x'
      - name: Restore dependencies
        run: dotnet restore budget-api/
      - name: Build
        run: dotnet build budget-api/ --no-restore
      - name: Test
        run: dotnet test budget-api/ --no-build --verbosity normal

  deploy:
    needs: [test-frontend, test-backend]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to production
        run: echo "Deploy to production"
```

### Environment Configuration
```bash
# Production environment variables
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=https://+:443;http://+:80
ConnectionStrings__DefaultConnection=Host=prod-postgres;Database=budget_management;Username=budget_user;Password=${DB_PASSWORD}
ConnectionStrings__Redis=prod-redis:6379
Jwt__SecretKey=${JWT_SECRET}
Jwt__Issuer=https://budget-api.company.com
Jwt__Audience=budget-management
Logging__LogLevel__Default=Information
```

## 📈 Scalability Considerations

### Horizontal Scaling
- **Load Balancer**: NGINX/HAProxy per distribuzione traffico
- **API Instances**: Multiple istanze Budget API
- **Database**: PostgreSQL con read replicas
- **Cache**: Redis Cluster per alta disponibilità

### Performance Optimization
- **Database Indexing**: Indici ottimizzati per query frequenti
- **Connection Pooling**: Pool connessioni database
- **Response Caching**: Cache HTTP per dati statici
- **CDN**: Content Delivery Network per asset statici

### Monitoring & Observability
- **Application Insights**: Telemetria applicazione
- **Prometheus + Grafana**: Metriche sistema
- **ELK Stack**: Centralized logging
- **Health Checks**: Monitoraggio stato servizi
