# Budget Management System - Documentazione di Modernizzazione

## 📋 Panoramica del Progetto

Il **Budget Management System** è un'applicazione moderna per la gestione dei budget aziendali, sviluppata seguendo i principi dell'architettura micro-frontend e delle tecnologie più recenti.

### 🎯 Obiettivi della Modernizzazione

- **Architettura Moderna**: Transizione da monolite a micro-frontend
- **Tecnologie Aggiornate**: Utilizzo di .NET 9, React 18, TypeScript
- **Scalabilità**: Architettura modulare e distribuita
- **Developer Experience**: Hot-reload, TypeScript, tooling moderno
- **Container-Ready**: Supporto completo per containerizzazione

## 🏗️ Architettura del Sistema

### Architettura Generale
```
┌─────────────────────────────────────────────────────────────┐
│                    PORTAL (Shell App)                      │
│                   http://localhost:3000                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Budget FE     │  │   Future FE     │  │  Shared     │ │
│  │ (Micro-frontend)│  │   Modules       │  │ Components  │ │
│  │ localhost:3001  │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     BACKEND APIs                           │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   Budget API    │  │    Auth API     │                  │
│  │ localhost:5000  │  │ localhost:5001  │                  │
│  │    (.NET 9)     │  │    (.NET 9)     │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   INFRASTRUCTURE                           │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   PostgreSQL    │  │      Redis      │                  │
│  │ localhost:5432  │  │ localhost:6379  │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

### Componenti Principali

#### 1. **Portal (Shell Application)**
- **Tecnologia**: React 18 + TypeScript + Vite
- **Ruolo**: Applicazione contenitore principale
- **Responsabilità**:
  - Routing principale
  - Autenticazione globale
  - Layout comune
  - Caricamento dinamico dei micro-frontend

#### 2. **Budget Frontend (Micro-frontend)**
- **Tecnologia**: React 18 + TypeScript + Vite + Ant Design
- **Ruolo**: Modulo specifico per gestione budget
- **Responsabilità**:
  - Interfaccia utente per budget
  - Gestione stato locale
  - Comunicazione con Budget API

#### 3. **Budget API**
- **Tecnologia**: .NET 9 + Entity Framework Core
- **Ruolo**: Servizio backend per gestione budget
- **Responsabilità**:
  - Business logic per budget
  - Accesso ai dati
  - Validazione e sicurezza

#### 4. **Infrastructure**
- **Database**: PostgreSQL 16
- **Cache**: Redis 7
- **Container Runtime**: Podman

## 🚀 Tecnologie Utilizzate

### Frontend Stack
- **React 18**: Framework UI con Concurrent Features
- **TypeScript 5**: Type safety e developer experience
- **Vite 5**: Build tool veloce con HMR
- **Ant Design 5**: Component library enterprise
- **Module Federation**: Micro-frontend architecture
- **React Query**: State management per API calls
- **React Hook Form**: Gestione form performante
- **Zustand**: State management leggero

### Backend Stack
- **NET 9**: Framework backend moderno
- **Entity Framework Core**: ORM per accesso dati
- **MediatR**: Pattern CQRS e mediator
- **AutoMapper**: Object mapping
- **FluentValidation**: Validazione robusta
- **Swagger/OpenAPI**: Documentazione API

### Infrastructure
- **PostgreSQL 16**: Database relazionale
- **Redis 7**: Cache in-memory
- **Podman**: Container runtime rootless
- **Docker Compose**: Orchestrazione servizi

## 📁 Struttura del Progetto

```
pianificazione_tecnica/
├── 📁 portal/                    # Shell application
│   ├── src/
│   │   ├── components/           # Componenti condivisi
│   │   ├── pages/               # Pagine principali
│   │   ├── services/            # Servizi API
│   │   └── types/               # Type definitions
│   ├── package.json
│   └── vite.config.ts           # Config Module Federation
├── 📁 budget-fe/                # Budget micro-frontend
│   ├── src/
│   │   ├── components/          # Componenti budget
│   │   ├── services/            # API budget
│   │   └── types/               # Types budget
│   ├── package.json
│   └── vite.config.ts           # Config Module Federation
├── 📁 budget-api/               # Budget API (.NET)
│   ├── src/
│   │   ├── BudgetManagement.WebAPI/
│   │   ├── BudgetManagement.Application/
│   │   ├── BudgetManagement.Domain/
│   │   └── BudgetManagement.Infrastructure/
│   └── Dockerfile
├── 📁 shared/                   # Componenti condivisi
│   └── api-contracts/           # Contratti API
├── 📁 docs/                     # Documentazione
├── docker-compose.yml           # Orchestrazione servizi
├── config.env.template          # Template configurazione
└── start-local.sh              # Script avvio rapido
```

## 🔧 Configurazione e Setup

### Prerequisiti
- **Node.js 20+**: Per sviluppo frontend
- **.NET 9 SDK**: Per sviluppo backend
- **Podman**: Per containerizzazione
- **PostgreSQL**: Database (incluso in compose)

### Avvio Rapido
```bash
# 1. Clona e configura
git clone <repository-url>
cd pianificazione_tecnica
cp config.env.template config.env

# 2. Avvia infrastruttura
podman-compose up -d postgres redis

# 3. Avvia frontend in modalità sviluppo
cd budget-fe && npm install && npm run dev &
cd ../portal && npm install && npm run dev &

# 4. Accedi al sistema
# Portal: http://localhost:3000
# Budget FE: http://localhost:3001
```

## 📊 Benefici della Modernizzazione

### Tecnici
- **Performance**: Vite build tool, lazy loading, code splitting
- **Scalabilità**: Architettura micro-frontend modulare
- **Manutenibilità**: TypeScript, clean architecture, separation of concerns
- **Developer Experience**: Hot reload, type safety, modern tooling

### Business
- **Time to Market**: Sviluppo parallelo di moduli
- **Flessibilità**: Deploy indipendente dei micro-frontend
- **Riusabilità**: Componenti condivisi tra moduli
- **Evoluzione**: Facile aggiunta di nuovi moduli

## 🔄 Prossimi Passi

1. **Completamento Backend**: Implementazione completa Budget API
2. **Autenticazione**: Integrazione Azure AD / OAuth
3. **Testing**: Unit test, integration test, E2E test
4. **CI/CD**: Pipeline automatizzate
5. **Monitoring**: Logging, metrics, health checks
6. **Nuovi Moduli**: Espansione con altri micro-frontend
