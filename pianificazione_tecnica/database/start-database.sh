#!/bin/bash

# Script per avviare il database PostgreSQL 17 con Podman
# Compatibile con Docker Compose

set -e

echo "🗄️  Avvio Database PostgreSQL 17 per Budget System..."

# Verifica se Podman è installato
if ! command -v podman &> /dev/null; then
    echo "❌ Podman non è installato. Installalo prima di continuare."
    echo "   Su macOS: brew install podman"
    echo "   Su Ubuntu: sudo apt install podman"
    exit 1
fi

# Verifica se podman-compose è installato
if ! command -v podman-compose &> /dev/null; then
    echo "⚠️  podman-compose non trovato. Installazione..."
    pip3 install podman-compose
fi

# Directory del database
DB_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$DB_DIR"

echo "📁 Directory database: $DB_DIR"

# Crea le directory necessarie se non esistono
mkdir -p backups
mkdir -p data

# Ferma eventuali container esistenti
echo "🛑 Fermata container esistenti..."
podman-compose down 2>/dev/null || true

# Avvia i servizi
echo "🚀 Avvio servizi database..."
podman-compose up -d

# Attendi che PostgreSQL sia pronto
echo "⏳ Attesa avvio PostgreSQL..."
sleep 10

# Verifica che PostgreSQL sia in esecuzione
echo "🔍 Verifica stato servizi..."
if podman-compose ps | grep -q "Up"; then
    echo "✅ Database PostgreSQL 17 avviato con successo!"
    echo ""
    echo "📊 Informazioni di connessione:"
    echo "   Host: localhost"
    echo "   Port: 5432"
    echo "   Database: budget_db"
    echo "   Username: budget_user"
    echo "   Password: budget_pass"
    echo ""
    echo "🌐 PgAdmin disponibile su: http://localhost:8080"
    echo "   Email: <EMAIL>"
    echo "   Password: admin123"
    echo ""
    echo "🔧 Comandi utili:"
    echo "   Connetti al DB: podman exec -it budget-postgres psql -U budget_user -d budget_db"
    echo "   Visualizza logs: podman-compose logs -f postgres"
    echo "   Ferma servizi: podman-compose down"
    echo "   Backup DB: ./backup-database.sh"
    echo ""
else
    echo "❌ Errore nell'avvio del database. Controlla i logs:"
    podman-compose logs postgres
    exit 1
fi

# Test connessione database
echo "🧪 Test connessione database..."
if podman exec budget-postgres pg_isready -U budget_user -d budget_db > /dev/null 2>&1; then
    echo "✅ Database pronto per le connessioni!"
    
    # Mostra statistiche database
    echo ""
    echo "📈 Statistiche database:"
    podman exec -it budget-postgres psql -U budget_user -d budget_db -c "
        SELECT 
            schemaname,
            tablename,
            n_tup_ins as inserimenti,
            n_tup_upd as aggiornamenti,
            n_tup_del as eliminazioni
        FROM pg_stat_user_tables 
        ORDER BY schemaname, tablename;
    " 2>/dev/null || echo "   Database inizializzato, statistiche disponibili dopo il primo utilizzo"
    
else
    echo "⚠️  Database avviato ma non ancora pronto. Riprova tra qualche secondo."
fi

echo ""
echo "🎉 Setup completato! Il database è pronto per l'uso."
