-- Budget Database Schema
-- PostgreSQL 17 compatible

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create custom types
CREATE TYPE plant_type AS ENUM ('THERMAL', 'HYDROELECTRIC');
CREATE TYPE user_type AS ENUM ('HEADQUARTERS', 'CROSS', 'THERMAL', 'HYDROELECTRIC');
CREATE TYPE budget_status AS ENUM ('DRAFT', 'SUBMITTED', 'APPROVED_PLANT', 'APPROVED_HEADQUARTERS', 'REFERENCE', 'REJECTED');
CREATE TYPE maintenance_plan_status AS ENUM ('DRAFT', 'SUBMITTED', 'APPROVED', 'REFERENCE', 'PRO_BUDGET');
CREATE TYPE maintenance_plan_type AS ENUM ('PROGRAMMED', 'EXTRAORDINARY', 'EMERGENCY');
CREATE TYPE intervention_priority AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- Users and Authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    user_type user_type NOT NULL,
    has_access_to_thermal BOOLEAN DEFAULT FALSE,
    has_access_to_hydroelectric BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- Plants
CREATE TABLE plants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    type plant_type NOT NULL,
    location VARCHAR(255),
    capacity_mw DECIMAL(10,2),
    commissioning_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Production Units
CREATE TABLE production_units (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plant_id UUID NOT NULL REFERENCES plants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20),
    capacity_mw DECIMAL(10,2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(plant_id, code)
);

-- KPI Categories
CREATE TABLE kpi_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    color VARCHAR(7) NOT NULL, -- Hex color code
    display_order INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- KPIs
CREATE TABLE kpis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    category_id UUID NOT NULL REFERENCES kpi_categories(id),
    is_auto_calculated BOOLEAN DEFAULT FALSE,
    calculation_formula TEXT,
    display_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Maintenance Plans
CREATE TABLE maintenance_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    year INTEGER NOT NULL,
    plant_id UUID NOT NULL REFERENCES plants(id),
    status maintenance_plan_status NOT NULL DEFAULT 'DRAFT',
    type maintenance_plan_type NOT NULL,
    total_cost DECIMAL(15,2) DEFAULT 0,
    is_reference BOOLEAN DEFAULT FALSE,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES users(id)
);

-- Maintenance Interventions
CREATE TABLE maintenance_interventions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    maintenance_plan_id UUID NOT NULL REFERENCES maintenance_plans(id) ON DELETE CASCADE,
    code VARCHAR(20) NOT NULL,
    description TEXT NOT NULL,
    planned_month INTEGER NOT NULL CHECK (planned_month >= 1 AND planned_month <= 12),
    estimated_cost DECIMAL(12,2) NOT NULL,
    category VARCHAR(100),
    priority intervention_priority NOT NULL DEFAULT 'MEDIUM',
    equipment VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(maintenance_plan_id, code)
);

-- Budgets
CREATE TABLE budgets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plant_id UUID NOT NULL REFERENCES plants(id),
    production_unit_id UUID NOT NULL REFERENCES production_units(id),
    year INTEGER NOT NULL,
    status budget_status NOT NULL DEFAULT 'DRAFT',
    version INTEGER NOT NULL DEFAULT 1,
    maintenance_plan_id UUID REFERENCES maintenance_plans(id),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP WITH TIME ZONE,
    submitted_by UUID REFERENCES users(id),
    approved_plant_at TIMESTAMP WITH TIME ZONE,
    approved_plant_by UUID REFERENCES users(id),
    approved_headquarters_at TIMESTAMP WITH TIME ZONE,
    approved_headquarters_by UUID REFERENCES users(id),
    rejected_at TIMESTAMP WITH TIME ZONE,
    rejected_by UUID REFERENCES users(id),
    rejection_reason TEXT,
    set_reference_at TIMESTAMP WITH TIME ZONE,
    set_reference_by UUID REFERENCES users(id),
    UNIQUE(plant_id, production_unit_id, year, version)
);

-- Budget Items (Monthly values for each KPI)
CREATE TABLE budget_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    budget_id UUID NOT NULL REFERENCES budgets(id) ON DELETE CASCADE,
    kpi_id UUID NOT NULL REFERENCES kpis(id),
    month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
    proposed_value DECIMAL(15,4),
    budget_value DECIMAL(15,4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(budget_id, kpi_id, month)
);

-- Budget History (Audit trail)
CREATE TABLE budget_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    budget_id UUID NOT NULL REFERENCES budgets(id),
    action VARCHAR(50) NOT NULL, -- 'CREATED', 'UPDATED', 'SUBMITTED', 'APPROVED', 'REJECTED', etc.
    old_status budget_status,
    new_status budget_status,
    changed_by UUID NOT NULL REFERENCES users(id),
    change_reason TEXT,
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB -- Additional data about the change
);

-- User Sessions (for authentication)
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT
);

-- Create indexes for performance
CREATE INDEX idx_plants_type ON plants(type);
CREATE INDEX idx_plants_active ON plants(is_active);
CREATE INDEX idx_production_units_plant ON production_units(plant_id);
CREATE INDEX idx_kpis_category ON kpis(category_id);
CREATE INDEX idx_kpis_active ON kpis(is_active);
CREATE INDEX idx_maintenance_plans_plant_year ON maintenance_plans(plant_id, year);
CREATE INDEX idx_maintenance_plans_status ON maintenance_plans(status);
CREATE INDEX idx_maintenance_interventions_plan ON maintenance_interventions(maintenance_plan_id);
CREATE INDEX idx_budgets_plant_year ON budgets(plant_id, year);
CREATE INDEX idx_budgets_status ON budgets(status);
CREATE INDEX idx_budgets_created_by ON budgets(created_by);
CREATE INDEX idx_budget_items_budget ON budget_items(budget_id);
CREATE INDEX idx_budget_items_kpi ON budget_items(kpi_id);
CREATE INDEX idx_budget_items_month ON budget_items(month);
CREATE INDEX idx_budget_history_budget ON budget_history(budget_id);
CREATE INDEX idx_budget_history_changed_at ON budget_history(changed_at);
CREATE INDEX idx_user_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_plants_updated_at BEFORE UPDATE ON plants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_production_units_updated_at BEFORE UPDATE ON production_units FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kpi_categories_updated_at BEFORE UPDATE ON kpi_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kpis_updated_at BEFORE UPDATE ON kpis FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_plans_updated_at BEFORE UPDATE ON maintenance_plans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_interventions_updated_at BEFORE UPDATE ON maintenance_interventions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_budgets_updated_at BEFORE UPDATE ON budgets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_budget_items_updated_at BEFORE UPDATE ON budget_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_sessions_updated_at BEFORE UPDATE ON user_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
