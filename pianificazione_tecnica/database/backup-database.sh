#!/bin/bash

# Script per backup del database PostgreSQL
set -e

echo "💾 Backup Database PostgreSQL..."

# Verifica che il container sia in esecuzione
if ! podman ps | grep -q "budget-postgres"; then
    echo "❌ Container PostgreSQL non in esecuzione. Avvialo prima con ./start-database.sh"
    exit 1
fi

# Crea directory backup se non esiste
mkdir -p backups

# Nome file backup con timestamp
BACKUP_FILE="backups/budget_db_backup_$(date +%Y%m%d_%H%M%S).sql"

echo "📁 Creazione backup: $BACKUP_FILE"

# Esegui backup
podman exec budget-postgres pg_dump -U budget_user -d budget_db > "$BACKUP_FILE"

# Comprimi il backup
gzip "$BACKUP_FILE"
BACKUP_FILE="${BACKUP_FILE}.gz"

echo "✅ Backup completato: $BACKUP_FILE"
echo "📊 Dimensione backup: $(du -h "$BACKUP_FILE" | cut -f1)"

# Mantieni solo gli ultimi 10 backup
echo "🧹 Pulizia backup vecchi..."
ls -t backups/budget_db_backup_*.sql.gz | tail -n +11 | xargs -r rm
echo "✅ Mantenuti gli ultimi 10 backup"

echo "🎉 Backup completato con successo!"
