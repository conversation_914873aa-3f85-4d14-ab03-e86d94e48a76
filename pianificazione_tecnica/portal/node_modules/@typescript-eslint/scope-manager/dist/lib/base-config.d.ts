export declare const TYPE: Readonly<{
    eslintImplicitGlobalSetting: "readonly";
    isTypeVariable: true;
    isValueVariable: false;
}>;
export declare const VALUE: Readonly<{
    eslintImplicitGlobalSetting: "readonly";
    isTypeVariable: false;
    isValueVariable: true;
}>;
export declare const TYPE_VALUE: Readonly<{
    eslintImplicitGlobalSetting: "readonly";
    isTypeVariable: true;
    isValueVariable: true;
}>;
//# sourceMappingURL=base-config.d.ts.map