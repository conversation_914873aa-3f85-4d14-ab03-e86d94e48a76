import {
  __commonJS
} from "./chunk-4MBMRILA.js";

// node_modules/@babel/runtime/helpers/interopRequireDefault.js
var require_interopRequireDefault = __commonJS({
  "node_modules/@babel/runtime/helpers/interopRequireDefault.js"(exports, module) {
    function _interopRequireDefault(e) {
      return e && e.__esModule ? e : {
        "default": e
      };
    }
    module.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/rc-pagination/lib/locale/it_IT.js
var require_it_IT = __commonJS({
  "node_modules/rc-pagination/lib/locale/it_IT.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var locale = {
      // Options
      items_per_page: "/ pagina",
      jump_to: "vai a",
      jump_to_confirm: "Conferma",
      page: "Pagina",
      // Pagination
      prev_page: "Pagina precedente",
      next_page: "Pagina successiva",
      prev_5: "Precedente 5 pagine",
      next_5: "Prossime 5 pagine",
      prev_3: "Precedente 3 pagine",
      next_3: "Prossime 3 pagine",
      page_size: "dimensioni della pagina"
    };
    var _default = exports.default = locale;
  }
});

// node_modules/@babel/runtime/helpers/typeof.js
var require_typeof = __commonJS({
  "node_modules/@babel/runtime/helpers/typeof.js"(exports, module) {
    function _typeof(o) {
      "@babel/helpers - typeof";
      return module.exports = _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
        return typeof o2;
      } : function(o2) {
        return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
      }, module.exports.__esModule = true, module.exports["default"] = module.exports, _typeof(o);
    }
    module.exports = _typeof, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/toPrimitive.js
var require_toPrimitive = __commonJS({
  "node_modules/@babel/runtime/helpers/toPrimitive.js"(exports, module) {
    var _typeof = require_typeof()["default"];
    function toPrimitive(t, r) {
      if ("object" != _typeof(t) || !t) return t;
      var e = t[Symbol.toPrimitive];
      if (void 0 !== e) {
        var i = e.call(t, r || "default");
        if ("object" != _typeof(i)) return i;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return ("string" === r ? String : Number)(t);
    }
    module.exports = toPrimitive, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/toPropertyKey.js
var require_toPropertyKey = __commonJS({
  "node_modules/@babel/runtime/helpers/toPropertyKey.js"(exports, module) {
    var _typeof = require_typeof()["default"];
    var toPrimitive = require_toPrimitive();
    function toPropertyKey(t) {
      var i = toPrimitive(t, "string");
      return "symbol" == _typeof(i) ? i : i + "";
    }
    module.exports = toPropertyKey, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/defineProperty.js
var require_defineProperty = __commonJS({
  "node_modules/@babel/runtime/helpers/defineProperty.js"(exports, module) {
    var toPropertyKey = require_toPropertyKey();
    function _defineProperty(e, r, t) {
      return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: true,
        configurable: true,
        writable: true
      }) : e[r] = t, e;
    }
    module.exports = _defineProperty, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/objectSpread2.js
var require_objectSpread2 = __commonJS({
  "node_modules/@babel/runtime/helpers/objectSpread2.js"(exports, module) {
    var defineProperty = require_defineProperty();
    function ownKeys(e, r) {
      var t = Object.keys(e);
      if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r2) {
          return Object.getOwnPropertyDescriptor(e, r2).enumerable;
        })), t.push.apply(t, o);
      }
      return t;
    }
    function _objectSpread2(e) {
      for (var r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), true).forEach(function(r2) {
          defineProperty(e, r2, t[r2]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r2) {
          Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
        });
      }
      return e;
    }
    module.exports = _objectSpread2, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/rc-picker/lib/locale/common.js
var require_common = __commonJS({
  "node_modules/rc-picker/lib/locale/common.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.commonLocale = void 0;
    var commonLocale = exports.commonLocale = {
      yearFormat: "YYYY",
      dayFormat: "D",
      cellMeridiemFormat: "A",
      monthBeforeYear: true
    };
  }
});

// node_modules/rc-picker/lib/locale/it_IT.js
var require_it_IT2 = __commonJS({
  "node_modules/rc-picker/lib/locale/it_IT.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _objectSpread2 = _interopRequireDefault(require_objectSpread2());
    var _common = require_common();
    var locale = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _common.commonLocale), {}, {
      locale: "it_IT",
      today: "Oggi",
      now: "Adesso",
      backToToday: "Torna ad oggi",
      ok: "OK",
      clear: "Cancella",
      week: "Settimana",
      month: "Mese",
      year: "Anno",
      timeSelect: "Seleziona l'ora",
      dateSelect: "Seleziona la data",
      monthSelect: "Seleziona il mese",
      yearSelect: "Seleziona l'anno",
      decadeSelect: "Seleziona il decennio",
      dateFormat: "D/M/YYYY",
      dateTimeFormat: "D/M/YYYY HH:mm:ss",
      previousMonth: "Il mese scorso (PageUp)",
      nextMonth: "Il prossimo mese (PageDown)",
      previousYear: "L'anno scorso (Control + sinistra)",
      nextYear: "L'anno prossimo (Control + destra)",
      previousDecade: "Ultimo decennio",
      nextDecade: "Prossimo decennio",
      previousCentury: "Secolo precedente",
      nextCentury: "Prossimo secolo"
    });
    var _default = exports.default = locale;
  }
});

// node_modules/antd/lib/time-picker/locale/it_IT.js
var require_it_IT3 = __commonJS({
  "node_modules/antd/lib/time-picker/locale/it_IT.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var locale = {
      placeholder: "Selezionare l'orario",
      rangePlaceholder: ["Inizio orario", "Fine orario"]
    };
    var _default = exports.default = locale;
  }
});

// node_modules/antd/lib/date-picker/locale/it_IT.js
var require_it_IT4 = __commonJS({
  "node_modules/antd/lib/date-picker/locale/it_IT.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _it_IT = _interopRequireDefault(require_it_IT2());
    var _it_IT2 = _interopRequireDefault(require_it_IT3());
    var locale = {
      lang: Object.assign({
        placeholder: "Selezionare la data",
        rangePlaceholder: ["Data d'inizio", "Data di fine"]
      }, _it_IT.default),
      timePickerLocale: Object.assign({}, _it_IT2.default)
    };
    var _default = exports.default = locale;
  }
});

// node_modules/antd/lib/calendar/locale/it_IT.js
var require_it_IT5 = __commonJS({
  "node_modules/antd/lib/calendar/locale/it_IT.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _it_IT = _interopRequireDefault(require_it_IT4());
    var _default = exports.default = _it_IT.default;
  }
});

// node_modules/antd/lib/locale/it_IT.js
var require_it_IT6 = __commonJS({
  "node_modules/antd/lib/locale/it_IT.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _it_IT = _interopRequireDefault(require_it_IT());
    var _it_IT2 = _interopRequireDefault(require_it_IT5());
    var _it_IT3 = _interopRequireDefault(require_it_IT4());
    var _it_IT4 = _interopRequireDefault(require_it_IT3());
    var typeTemplate = " ${label} non è un ${type} valido";
    var localeValues = {
      locale: "it",
      Pagination: _it_IT.default,
      DatePicker: _it_IT3.default,
      TimePicker: _it_IT4.default,
      Calendar: _it_IT2.default,
      global: {
        placeholder: "Selezionare",
        close: "Chiudi"
      },
      Table: {
        filterTitle: "Menù Filtro",
        filterConfirm: "OK",
        filterReset: "Reset",
        filterEmptyText: "Senza filtri",
        filterCheckAll: "Seleziona tutti",
        filterSearchPlaceholder: "Cerca nei filtri",
        emptyText: "Senza dati",
        selectAll: "Seleziona pagina corrente",
        selectInvert: "Inverti selezione nella pagina corrente",
        selectNone: "Deseleziona tutto",
        selectionAll: "Seleziona tutto",
        sortTitle: "Ordina",
        expand: "Espandi riga",
        collapse: "Comprimi riga ",
        triggerDesc: "Clicca per ordinare in modo discendente",
        triggerAsc: "Clicca per ordinare in modo ascendente",
        cancelSort: "Clicca per eliminare l'ordinamento"
      },
      Tour: {
        Next: "Successivo",
        Previous: "Precedente",
        Finish: "Termina"
      },
      Modal: {
        okText: "OK",
        cancelText: "Annulla",
        justOkText: "OK"
      },
      Popconfirm: {
        okText: "OK",
        cancelText: "Annulla"
      },
      Transfer: {
        titles: ["", ""],
        searchPlaceholder: "Cerca qui",
        itemUnit: "elemento",
        itemsUnit: "elementi",
        remove: "Elimina",
        selectCurrent: "Seleziona la pagina corrente",
        removeCurrent: "Rimuovi la pagina corrente",
        selectAll: "Seleziona tutti i dati",
        removeAll: "Rimuovi tutti i dati",
        selectInvert: "Inverti la pagina corrente"
      },
      Upload: {
        uploading: "Caricamento...",
        removeFile: "Rimuovi il file",
        uploadError: "Errore di caricamento",
        previewFile: "Anteprima file",
        downloadFile: "Scarica file"
      },
      Empty: {
        description: "Nessun dato"
      },
      Icon: {
        icon: "icona"
      },
      Text: {
        edit: "modifica",
        copy: "copia",
        copied: "copia effettuata",
        expand: "espandi"
      },
      Form: {
        optional: "(opzionale)",
        defaultValidateMessages: {
          default: "Errore di convalida del campo ${label}",
          required: "Si prega di inserire ${label}",
          enum: "${label} deve essere uno di [${enum}]",
          whitespace: "${label} non può essere un carattere vuoto",
          date: {
            format: "Il formato della data ${label} non è valido",
            parse: "${label} non può essere convertito in una data",
            invalid: "${label} non è una data valida"
          },
          types: {
            string: typeTemplate,
            method: typeTemplate,
            array: typeTemplate,
            object: typeTemplate,
            number: typeTemplate,
            date: typeTemplate,
            boolean: typeTemplate,
            integer: typeTemplate,
            float: typeTemplate,
            regexp: typeTemplate,
            email: typeTemplate,
            url: typeTemplate,
            hex: typeTemplate
          },
          string: {
            len: "${label} deve avere ${len} caratteri",
            min: "${label} deve contenere almeno ${min} caratteri",
            max: "${label} deve contenere fino a ${max} caratteri",
            range: "${label} deve contenere tra ${min}-${max} caratteri"
          },
          number: {
            len: "${label} deve essere uguale a ${len}",
            min: "${label} valore minimo è ${min}",
            max: "${label} valor e massimo è ${max}",
            range: "${label} deve essere compreso tra ${min}-${max}"
          },
          array: {
            len: "Deve essere ${len} ${label}",
            min: "Almeno ${min} ${label}",
            max: "Massimo ${max} ${label}",
            range: "Il totale di ${label} deve essere compreso tra ${min}-${max}"
          },
          pattern: {
            mismatch: "${label} non corrisponde al modello ${pattern}"
          }
        }
      },
      Image: {
        preview: "Anteprima"
      }
    };
    var _default = exports.default = localeValues;
  }
});

// node_modules/antd/locale/it_IT.js
var require_it_IT7 = __commonJS({
  "node_modules/antd/locale/it_IT.js"(exports, module) {
    module.exports = require_it_IT6();
  }
});
export default require_it_IT7();
//# sourceMappingURL=antd_locale_it_IT.js.map
