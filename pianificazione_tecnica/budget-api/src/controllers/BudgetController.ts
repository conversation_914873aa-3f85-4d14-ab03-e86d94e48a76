import { Request, Response } from 'express';
import { BudgetService } from '@/services/BudgetService';
import { CreateBudgetRequest, UpdateBudgetRequest, BudgetFilters, ApiResponse } from '@/types';
import { logger } from '@/utils/logger';
import { validateCreateBudget, validateUpdateBudget, validateBudgetFilters } from '@/validators/budgetValidators';

export class BudgetController {
  private budgetService: BudgetService;

  constructor() {
    this.budgetService = new BudgetService();
  }

  async createBudget(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const { error, value } = validateCreateBudget(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(d => d.message),
        } as ApiResponse);
        return;
      }

      const userId = req.user!.id;
      const budget = await this.budgetService.createBudget(value as CreateBudgetRequest, userId);

      res.status(201).json({
        success: true,
        data: budget,
        message: 'Budget created successfully',
      } as ApiResponse);

    } catch (error) {
      logger.error('Error creating budget:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async getBudgets(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const { error, value } = validateBudgetFilters(req.query);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(d => d.message),
        } as ApiResponse);
        return;
      }

      const userId = req.user!.id;
      const result = await this.budgetService.getBudgets(value as BudgetFilters, userId);

      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
      } as ApiResponse);

    } catch (error) {
      logger.error('Error getting budgets:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async getBudgetById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const budget = await this.budgetService.getBudgetById(id);

      res.json({
        success: true,
        data: budget,
      } as ApiResponse);

    } catch (error) {
      logger.error('Error getting budget by id:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async updateBudget(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      // Validate request body
      const { error, value } = validateUpdateBudget(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(d => d.message),
        } as ApiResponse);
        return;
      }

      const userId = req.user!.id;
      const budget = await this.budgetService.updateBudget(id, value as UpdateBudgetRequest, userId);

      res.json({
        success: true,
        data: budget,
        message: 'Budget updated successfully',
      } as ApiResponse);

    } catch (error) {
      logger.error('Error updating budget:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async deleteBudget(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      await this.budgetService.deleteBudget(id, userId);

      res.json({
        success: true,
        message: 'Budget deleted successfully',
      } as ApiResponse);

    } catch (error) {
      logger.error('Error deleting budget:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async submitBudget(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const budget = await this.budgetService.submitBudget(id, userId);

      res.json({
        success: true,
        data: budget,
        message: 'Budget submitted successfully',
      } as ApiResponse);

    } catch (error) {
      logger.error('Error submitting budget:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async approveBudgetAtPlant(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const budget = await this.budgetService.approveBudgetAtPlant(id, userId);

      res.json({
        success: true,
        data: budget,
        message: 'Budget approved at plant level',
      } as ApiResponse);

    } catch (error) {
      logger.error('Error approving budget at plant:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async approveBudgetAtHeadquarters(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const budget = await this.budgetService.approveBudgetAtHeadquarters(id, userId);

      res.json({
        success: true,
        data: budget,
        message: 'Budget approved at headquarters level',
      } as ApiResponse);

    } catch (error) {
      logger.error('Error approving budget at headquarters:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async rejectBudget(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      const userId = req.user!.id;

      if (!reason || typeof reason !== 'string' || reason.trim().length === 0) {
        res.status(400).json({
          success: false,
          message: 'Rejection reason is required',
        } as ApiResponse);
        return;
      }
      
      const budget = await this.budgetService.rejectBudget(id, reason.trim(), userId);

      res.json({
        success: true,
        data: budget,
        message: 'Budget rejected',
      } as ApiResponse);

    } catch (error) {
      logger.error('Error rejecting budget:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async setAsReference(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const budget = await this.budgetService.setAsReference(id, userId);

      res.json({
        success: true,
        data: budget,
        message: 'Budget set as reference',
      } as ApiResponse);

    } catch (error) {
      logger.error('Error setting budget as reference:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async copyFromProposal(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const result = await this.budgetService.copyFromProposal(id, userId);

      res.json({
        success: true,
        data: result,
        message: 'Values copied from proposal to budget',
      } as ApiResponse);

    } catch (error) {
      logger.error('Error copying from proposal:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async calculateAutomatic(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const result = await this.budgetService.calculateAutomatic(id, userId);

      res.json({
        success: true,
        data: result,
        message: 'Automatic KPIs calculated',
      } as ApiResponse);

    } catch (error) {
      logger.error('Error calculating automatic KPIs:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async calculateDerived(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      
      const result = await this.budgetService.calculateDerived(id, userId);

      res.json({
        success: true,
        data: result,
        message: 'Derived KPIs calculated',
      } as ApiResponse);

    } catch (error) {
      logger.error('Error calculating derived KPIs:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }

  async validateBudget(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const result = await this.budgetService.validateBudget(id);

      res.json({
        success: true,
        data: result,
      } as ApiResponse);

    } catch (error) {
      logger.error('Error validating budget:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      } as ApiResponse);
    }
  }
}
