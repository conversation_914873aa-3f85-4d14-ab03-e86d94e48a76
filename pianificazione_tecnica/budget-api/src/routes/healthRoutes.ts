import { Router, Request, Response } from 'express';
import { db } from '@/database/connection';
import { logger } from '@/utils/logger';

const router = Router();

// GET /api/v1/health - Basic health check
router.get('/', async (req: Request, res: Response) => {
  try {
    const dbHealth = await db.healthCheck();
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();

    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: `${Math.floor(uptime / 60)}m ${Math.floor(uptime % 60)}s`,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      database: {
        connected: dbHealth.isConnected,
        poolSize: dbHealth.poolSize,
        idleConnections: dbHealth.idleCount,
        waitingConnections: dbHealth.waitingCount,
      },
      memory: {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
      },
    };

    // Return 503 if database is not connected
    const statusCode = dbHealth.isConnected ? 200 : 503;
    
    res.status(statusCode).json({
      success: dbHealth.isConnected,
      data: health,
    });

  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      success: false,
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/v1/health/ready - Readiness probe
router.get('/ready', async (req: Request, res: Response) => {
  try {
    const dbHealth = await db.healthCheck();
    
    if (dbHealth.isConnected) {
      res.status(200).json({
        success: true,
        status: 'ready',
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(503).json({
        success: false,
        status: 'not ready',
        timestamp: new Date().toISOString(),
        reason: 'Database not connected',
      });
    }
  } catch (error) {
    res.status(503).json({
      success: false,
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// GET /api/v1/health/live - Liveness probe
router.get('/live', (req: Request, res: Response) => {
  res.status(200).json({
    success: true,
    status: 'alive',
    timestamp: new Date().toISOString(),
  });
});

export { router as healthRoutes };
