import { Router, Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { executeQueryOne } from '@/database/connection';
import { validateLogin } from '@/validators/budgetValidators';
import { LoginRequest, LoginResponse, ApiResponse, UserType } from '@/types';
import { logger, logAuth } from '@/utils/logger';

const router = Router();

// POST /api/v1/auth/login - User login
router.post('/login', async (req: Request, res: Response) => {
  try {
    // Validate request body
    const { error, value } = validateLogin(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(d => d.message),
      } as ApiResponse);
      return;
    }

    const { username, password } = value as LoginRequest;

    // Find user by username
    const user = await executeQueryOne(
      `SELECT id, username, email, password_hash, first_name, last_name, 
              user_type, has_access_to_thermal, has_access_to_hydroelectric, is_active
       FROM users 
       WHERE username = $1 AND is_active = true`,
      [username]
    );

    if (!user) {
      logAuth('LOGIN_FAILED', undefined, { username, reason: 'User not found' });
      res.status(401).json({
        success: false,
        message: 'Invalid credentials',
      } as ApiResponse);
      return;
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      logAuth('LOGIN_FAILED', user.id, { username, reason: 'Invalid password' });
      res.status(401).json({
        success: false,
        message: 'Invalid credentials',
      } as ApiResponse);
      return;
    }

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      logger.error('JWT_SECRET not configured');
      res.status(500).json({
        success: false,
        message: 'Server configuration error',
      } as ApiResponse);
      return;
    }

    const tokenPayload = {
      userId: user.id,
      username: user.username,
      userType: user.user_type,
    };

    const expiresIn = process.env.JWT_EXPIRES_IN || '24h';
    const token = jwt.sign(tokenPayload, jwtSecret, { expiresIn });

    // Update last login timestamp
    await executeQueryOne(
      'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = $1',
      [user.id]
    );

    // Prepare response
    const userResponse = {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      userType: user.user_type as UserType,
      hasAccessToThermal: user.has_access_to_thermal,
      hasAccessToHydroelectric: user.has_access_to_hydroelectric,
      isActive: user.is_active,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      lastLoginAt: new Date(),
    };

    logAuth('LOGIN_SUCCESS', user.id, { username });

    res.json({
      success: true,
      data: {
        user: userResponse,
        token,
        expiresIn,
      } as LoginResponse,
      message: 'Login successful',
    } as ApiResponse);

  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    } as ApiResponse);
  }
});

// POST /api/v1/auth/logout - User logout (optional, mainly for logging)
router.post('/logout', async (req: Request, res: Response) => {
  try {
    // In a stateless JWT system, logout is mainly handled client-side
    // This endpoint is mainly for logging purposes
    
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      try {
        const jwtSecret = process.env.JWT_SECRET;
        if (jwtSecret) {
          const decoded = jwt.verify(token, jwtSecret) as any;
          logAuth('LOGOUT', decoded.userId, { username: decoded.username });
        }
      } catch (error) {
        // Token might be expired or invalid, but that's ok for logout
      }
    }

    res.json({
      success: true,
      message: 'Logout successful',
    } as ApiResponse);

  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    } as ApiResponse);
  }
});

// GET /api/v1/auth/me - Get current user info (requires auth)
router.get('/me', async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        message: 'Access token required',
      } as ApiResponse);
      return;
    }

    const token = authHeader.substring(7);
    const jwtSecret = process.env.JWT_SECRET;
    
    if (!jwtSecret) {
      res.status(500).json({
        success: false,
        message: 'Server configuration error',
      } as ApiResponse);
      return;
    }

    const decoded = jwt.verify(token, jwtSecret) as any;
    
    // Get fresh user data
    const user = await executeQueryOne(
      `SELECT id, username, email, first_name, last_name, 
              user_type, has_access_to_thermal, has_access_to_hydroelectric, 
              is_active, created_at, updated_at, last_login_at
       FROM users 
       WHERE id = $1 AND is_active = true`,
      [decoded.userId]
    );

    if (!user) {
      res.status(401).json({
        success: false,
        message: 'User not found',
      } as ApiResponse);
      return;
    }

    const userResponse = {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      userType: user.user_type as UserType,
      hasAccessToThermal: user.has_access_to_thermal,
      hasAccessToHydroelectric: user.has_access_to_hydroelectric,
      isActive: user.is_active,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      lastLoginAt: user.last_login_at,
    };

    res.json({
      success: true,
      data: userResponse,
    } as ApiResponse);

  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      res.status(401).json({
        success: false,
        message: 'Token expired',
      } as ApiResponse);
      return;
    }

    if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({
        success: false,
        message: 'Invalid token',
      } as ApiResponse);
      return;
    }

    logger.error('Get user info error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    } as ApiResponse);
  }
});

export { router as authRoutes };
