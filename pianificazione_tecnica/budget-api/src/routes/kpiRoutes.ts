import { Router, Request, Response } from 'express';
import { KPIService } from '@/services/KPIService';
import { validateCreateKPI, validateCreateKPICategory } from '@/validators/budgetValidators';
import { ApiResponse } from '@/types';
import { logger } from '@/utils/logger';

const router = Router();
const kpiService = new KPIService();

// GET /api/v1/kpis/categories - Get all KPI categories
router.get('/categories', async (req: Request, res: Response) => {
  try {
    const categories = await kpiService.getAllKPICategories();

    res.json({
      success: true,
      data: categories,
    } as ApiResponse);

  } catch (error) {
    logger.error('Error getting KPI categories:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// POST /api/v1/kpis/categories - Create new KPI category
router.post('/categories', async (req: Request, res: Response) => {
  try {
    const { error, value } = validateCreateKPICategory(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(d => d.message),
      } as ApiResponse);
      return;
    }

    const category = await kpiService.createKPICategory(value);

    res.status(201).json({
      success: true,
      data: category,
      message: 'KPI category created successfully',
    } as ApiResponse);

  } catch (error) {
    logger.error('Error creating KPI category:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// PUT /api/v1/kpis/categories/:id - Update KPI category
router.put('/categories/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const { error, value } = validateCreateKPICategory(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(d => d.message),
      } as ApiResponse);
      return;
    }

    const category = await kpiService.updateKPICategory(id, value);

    res.json({
      success: true,
      data: category,
      message: 'KPI category updated successfully',
    } as ApiResponse);

  } catch (error) {
    logger.error('Error updating KPI category:', error);
    const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// DELETE /api/v1/kpis/categories/:id - Delete KPI category
router.delete('/categories/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await kpiService.deleteKPICategory(id);

    res.json({
      success: true,
      message: 'KPI category deleted successfully',
    } as ApiResponse);

  } catch (error) {
    logger.error('Error deleting KPI category:', error);
    const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// GET /api/v1/kpis - Get all KPIs
router.get('/', async (req: Request, res: Response) => {
  try {
    const categories = await kpiService.getAllKPICategories();
    
    res.json({
      success: true,
      data: categories,
    } as ApiResponse);

  } catch (error) {
    logger.error('Error getting KPI categories:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// POST /api/v1/kpis/categories - Create new KPI category
router.post('/categories', async (req: Request, res: Response) => {
  try {
    const { error, value } = validateCreateKPICategory(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(d => d.message),
      } as ApiResponse);
      return;
    }

    const category = await kpiService.createKPICategory(value);
    
    res.status(201).json({
      success: true,
      data: category,
      message: 'KPI category created successfully',
    } as ApiResponse);

  } catch (error) {
    logger.error('Error creating KPI category:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// GET /api/v1/kpis/:id - Get KPI by ID
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const kpi = await kpiService.getKPIById(id);
    
    if (!kpi) {
      res.status(404).json({
        success: false,
        message: 'KPI not found',
      } as ApiResponse);
      return;
    }

    res.json({
      success: true,
      data: kpi,
    } as ApiResponse);

  } catch (error) {
    logger.error('Error getting KPI by ID:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// POST /api/v1/kpis - Create new KPI
router.post('/', async (req: Request, res: Response) => {
  try {
    const { error, value } = validateCreateKPI(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(d => d.message),
      } as ApiResponse);
      return;
    }

    const kpi = await kpiService.createKPI(value);
    
    res.status(201).json({
      success: true,
      data: kpi,
      message: 'KPI created successfully',
    } as ApiResponse);

  } catch (error) {
    logger.error('Error creating KPI:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// PUT /api/v1/kpis/:id - Update KPI
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Use the same validation as create but make all fields optional
    const { error, value } = validateCreateKPI(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(d => d.message),
      } as ApiResponse);
      return;
    }

    const kpi = await kpiService.updateKPI(id, value);
    
    res.json({
      success: true,
      data: kpi,
      message: 'KPI updated successfully',
    } as ApiResponse);

  } catch (error) {
    logger.error('Error updating KPI:', error);
    const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// DELETE /api/v1/kpis/:id - Delete KPI (soft delete)
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await kpiService.deleteKPI(id);
    
    res.json({
      success: true,
      message: 'KPI deleted successfully',
    } as ApiResponse);

  } catch (error) {
    logger.error('Error deleting KPI:', error);
    const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});



export { router as kpiRoutes };
