import { Router, Request, Response } from 'express';
import { executeQuery, executeQueryOne } from '@/database/connection';
import { Plant, ProductionUnit, PlantType, ApiResponse } from '@/types';
import { logger } from '@/utils/logger';

const router = Router();

// GET /api/v1/plants/maintenance-plans - Get maintenance plans for plant and year
router.get('/maintenance-plans', async (req: Request, res: Response) => {
  try {
    const { plantId, year } = req.query;

    // Validate required parameters
    if (!plantId || !year) {
      res.status(400).json({
        success: false,
        message: 'plantId and year parameters are required',
      } as ApiResponse);
      return;
    }

    // Validate year
    const yearNum = parseInt(year as string);
    if (isNaN(yearNum) || yearNum < 2020 || yearNum > 2030) {
      res.status(400).json({
        success: false,
        message: 'Invalid year parameter',
      } as ApiResponse);
      return;
    }

    const query = `
      SELECT
        mp.*,
        p.name as plant_name,
        COUNT(mi.id) as interventions_count
      FROM maintenance_plans mp
      LEFT JOIN plants p ON mp.plant_id = p.id
      LEFT JOIN maintenance_interventions mi ON mp.id = mi.maintenance_plan_id
      WHERE mp.plant_id = $1 AND mp.year = $2
        AND mp.status IN ('REFERENCE', 'PRO_BUDGET')
      GROUP BY mp.id, p.name
      ORDER BY mp.status DESC, mp.created_at DESC
    `;

    const rows = await executeQuery(query, [plantId as string, yearNum]);
    const plans = rows.map(row => ({
      id: row.id,
      code: row.code,
      name: row.name,
      year: row.year,
      plantId: row.plant_id,
      status: row.status,
      type: row.type,
      totalCost: parseFloat(row.total_cost || '0'),
      isReference: row.is_reference,
      createdBy: row.created_by,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      approvedAt: row.approved_at,
      approvedBy: row.approved_by,
      interventionsCount: parseInt(row.interventions_count || '0'),
    }));

    res.json({
      success: true,
      data: plans,
    } as ApiResponse);

  } catch (error) {
    logger.error('Error getting maintenance plans:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// GET /api/v1/plants/maintenance-interventions - Get interventions for a maintenance plan
router.get('/maintenance-interventions', async (req: Request, res: Response) => {
  try {
    const { planId } = req.query;

    if (!planId) {
      res.status(400).json({
        success: false,
        message: 'planId parameter is required',
      } as ApiResponse);
      return;
    }

    const query = `
      SELECT * FROM maintenance_interventions
      WHERE maintenance_plan_id = $1
      ORDER BY planned_month, estimated_cost DESC
    `;

    const rows = await executeQuery(query, [planId as string]);
    const interventions = rows.map(row => ({
      id: row.id,
      maintenancePlanId: row.maintenance_plan_id,
      code: row.code,
      description: row.description,
      plannedMonth: row.planned_month,
      estimatedCost: parseFloat(row.estimated_cost || '0'),
      category: row.category,
      priority: row.priority,
      equipment: row.equipment,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    }));

    res.json({
      success: true,
      data: interventions,
    } as ApiResponse);

  } catch (error) {
    logger.error('Error getting maintenance interventions:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// GET /api/v1/plants - Get all plants
router.get('/', async (req: Request, res: Response) => {
  try {
    const { type } = req.query;

    let query = `
      SELECT p.*,
             COUNT(pu.id) as production_units_count
      FROM plants p
      LEFT JOIN production_units pu ON p.id = pu.plant_id AND pu.is_active = true
      WHERE p.is_active = true
    `;

    const params: any[] = [];

    if (type && (type === 'THERMAL' || type === 'HYDROELECTRIC')) {
      query += ' AND p.type = $1';
      params.push(type);
    }

    query += ' GROUP BY p.id ORDER BY p.name';

    const rows = await executeQuery(query, params);
    const plants = rows.map(mapRowToPlant);

    res.json({
      success: true,
      data: plants,
    } as ApiResponse);

  } catch (error) {
    logger.error('Error getting plants:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// GET /api/v1/plants/:id/production-units - Get production units for a plant
router.get('/:id/production-units', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // First check if plant exists
    const plant = await executeQueryOne(
      'SELECT id FROM plants WHERE id = $1 AND is_active = true',
      [id]
    );

    if (!plant) {
      res.status(404).json({
        success: false,
        message: 'Plant not found',
      } as ApiResponse);
      return;
    }

    const query = `
      SELECT * FROM production_units
      WHERE plant_id = $1 AND is_active = true
      ORDER BY name
    `;

    const rows = await executeQuery(query, [id]);
    const units = rows.map(mapRowToProductionUnit);

    res.json({
      success: true,
      data: units,
    } as ApiResponse);

  } catch (error) {
    logger.error('Error getting production units:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});

// GET /api/v1/plants/:id - Get plant by ID
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const query = `
      SELECT p.*, 
             COUNT(pu.id) as production_units_count
      FROM plants p
      LEFT JOIN production_units pu ON p.id = pu.plant_id AND pu.is_active = true
      WHERE p.id = $1 AND p.is_active = true
      GROUP BY p.id
    `;
    
    const row = await executeQueryOne(query, [id]);
    
    if (!row) {
      res.status(404).json({
        success: false,
        message: 'Plant not found',
      } as ApiResponse);
      return;
    }

    const plant = mapRowToPlant(row);
    
    res.json({
      success: true,
      data: plant,
    } as ApiResponse);

  } catch (error) {
    logger.error('Error getting plant by ID:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Internal server error',
    } as ApiResponse);
  }
});



// Helper functions
function mapRowToPlant(row: any): Plant {
  return {
    id: row.id,
    code: row.code,
    name: row.name,
    type: row.type as PlantType,
    location: row.location,
    capacityMw: row.capacity_mw ? parseFloat(row.capacity_mw) : undefined,
    commissioningDate: row.commissioning_date,
    isActive: row.is_active,
    createdAt: row.created_at,
    updatedAt: row.updated_at,
  };
}

function mapRowToProductionUnit(row: any): ProductionUnit {
  return {
    id: row.id,
    plantId: row.plant_id,
    name: row.name,
    code: row.code,
    capacityMw: row.capacity_mw ? parseFloat(row.capacity_mw) : undefined,
    isActive: row.is_active,
    createdAt: row.created_at,
    updatedAt: row.updated_at,
  };
}

export { router as plantRoutes };
