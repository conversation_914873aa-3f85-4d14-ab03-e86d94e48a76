import { Router } from 'express';
import { BudgetController } from '@/controllers/BudgetController';

const router = Router();
const budgetController = new BudgetController();

// GET /api/v1/budgets - Get all budgets with filters
router.get('/', budgetController.getBudgets.bind(budgetController));

// POST /api/v1/budgets - Create new budget
router.post('/', budgetController.createBudget.bind(budgetController));

// GET /api/v1/budgets/:id - Get budget by ID
router.get('/:id', budgetController.getBudgetById.bind(budgetController));

// PUT /api/v1/budgets/:id - Update budget
router.put('/:id', budgetController.updateBudget.bind(budgetController));

// DELETE /api/v1/budgets/:id - Delete budget
router.delete('/:id', budgetController.deleteBudget.bind(budgetController));

// POST /api/v1/budgets/:id/submit - Submit budget for approval
router.post('/:id/submit', budgetController.submitBudget.bind(budgetController));

// POST /api/v1/budgets/:id/approve_plant - Approve budget at plant level
router.post('/:id/approve_plant', budgetController.approveBudgetAtPlant.bind(budgetController));

// POST /api/v1/budgets/:id/approve_headquarters - Approve budget at headquarters level
router.post('/:id/approve_headquarters', budgetController.approveBudgetAtHeadquarters.bind(budgetController));

// POST /api/v1/budgets/:id/reject - Reject budget
router.post('/:id/reject', budgetController.rejectBudget.bind(budgetController));

// POST /api/v1/budgets/:id/set_reference - Set budget as reference
router.post('/:id/set_reference', budgetController.setAsReference.bind(budgetController));

// POST /api/v1/budgets/:id/copy_from_proposal - Copy values from proposal to budget
router.post('/:id/copy_from_proposal', budgetController.copyFromProposal.bind(budgetController));

// POST /api/v1/budgets/:id/calculate_automatic - Calculate automatic KPIs
router.post('/:id/calculate_automatic', budgetController.calculateAutomatic.bind(budgetController));

// POST /api/v1/budgets/:id/calculate_derived - Calculate derived KPIs
router.post('/:id/calculate_derived', budgetController.calculateDerived.bind(budgetController));

// GET /api/v1/budgets/:id/validate - Validate budget
router.get('/:id/validate', budgetController.validateBudget.bind(budgetController));

export { router as budgetRoutes };
