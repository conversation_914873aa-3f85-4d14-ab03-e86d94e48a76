import { Router } from 'express';
import { BudgetController } from '@/controllers/BudgetController';

const router = Router();
const budgetController = new BudgetController();

// GET /api/v1/budgets - Get all budgets with filters
router.get('/', budgetController.getBudgets.bind(budgetController));

// POST /api/v1/budgets - Create new budget
router.post('/', budgetController.createBudget.bind(budgetController));

// GET /api/v1/budgets/:id - Get budget by ID
router.get('/:id', budgetController.getBudgetById.bind(budgetController));

// PUT /api/v1/budgets/:id - Update budget
router.put('/:id', budgetController.updateBudget.bind(budgetController));

// DELETE /api/v1/budgets/:id - Delete budget
router.delete('/:id', budgetController.deleteBudget.bind(budgetController));

// POST /api/v1/budgets/:id/submit - Submit budget for approval
router.post('/:id/submit', budgetController.submitBudget.bind(budgetController));

// POST /api/v1/budgets/:id/approve-plant - Approve budget at plant level
router.post('/:id/approve-plant', budgetController.approveBudgetAtPlant.bind(budgetController));

// POST /api/v1/budgets/:id/approve-headquarters - Approve budget at headquarters level
router.post('/:id/approve-headquarters', budgetController.approveBudgetAtHeadquarters.bind(budgetController));

// POST /api/v1/budgets/:id/reject - Reject budget
router.post('/:id/reject', budgetController.rejectBudget.bind(budgetController));

// POST /api/v1/budgets/:id/set-reference - Set budget as reference
router.post('/:id/set-reference', budgetController.setAsReference.bind(budgetController));

// POST /api/v1/budgets/:id/copy-from-proposal - Copy values from proposal to budget
router.post('/:id/copy-from-proposal', budgetController.copyFromProposal.bind(budgetController));

// POST /api/v1/budgets/:id/calculate-automatic - Calculate automatic KPIs
router.post('/:id/calculate-automatic', budgetController.calculateAutomatic.bind(budgetController));

// POST /api/v1/budgets/:id/calculate-derived - Calculate derived KPIs
router.post('/:id/calculate-derived', budgetController.calculateDerived.bind(budgetController));

// GET /api/v1/budgets/:id/validate - Validate budget
router.get('/:id/validate', budgetController.validateBudget.bind(budgetController));

export { router as budgetRoutes };
