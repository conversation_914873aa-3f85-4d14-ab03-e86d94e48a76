// Shared types for Budget API

export enum PlantType {
  THERMAL = 'THERMAL',
  HYDROELECTRIC = 'HYDROELECTRIC'
}

export enum UserType {
  HEADQUARTERS = 'HEADQUARTERS',
  CROSS = 'CROSS', 
  THERMAL = 'THERMAL',
  HYDROELECTRIC = 'HYDROELECTRIC'
}

export enum BudgetStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  APPROVED_PLANT = 'APPROVED_PLANT',
  APPROVED_HEADQUARTERS = 'APPROVED_HEADQUARTERS',
  REFERENCE = 'REFERENCE',
  REJECTED = 'REJECTED'
}

export enum MaintenancePlanStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  APPROVED = 'APPROVED',
  REFERENCE = 'REFERENCE',
  PRO_BUDGET = 'PRO_BUDGET'
}

export enum MaintenancePlanType {
  PROGRAMMED = 'PROGRAMMED',
  EXTRAORDINARY = 'EXTRAORDINARY',
  EMERGENCY = 'EMERGENCY'
}

export enum InterventionPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Database entities
export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  userType: UserType;
  hasAccessToThermal: boolean;
  hasAccessToHydroelectric: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
}

export interface Plant {
  id: string;
  code: string;
  name: string;
  type: PlantType;
  location?: string;
  capacityMw?: number;
  commissioningDate?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductionUnit {
  id: string;
  plantId: string;
  name: string;
  code?: string;
  capacityMw?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  plant?: Plant;
}

export interface KPICategory {
  id: string;
  name: string;
  color: string;
  displayOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface KPI {
  id: string;
  code: string;
  name: string;
  unitOfMeasure: string;
  categoryId: string;
  isAutoCalculated: boolean;
  calculationFormula?: string;
  displayOrder: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  category?: KPICategory;
}

export interface MaintenancePlan {
  id: string;
  code: string;
  name: string;
  year: number;
  plantId: string;
  status: MaintenancePlanStatus;
  type: MaintenancePlanType;
  totalCost: number;
  isReference: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  approvedAt?: Date;
  approvedBy?: string;
  plant?: Plant;
  interventions?: MaintenanceIntervention[];
}

export interface MaintenanceIntervention {
  id: string;
  maintenancePlanId: string;
  code: string;
  description: string;
  plannedMonth: number;
  estimatedCost: number;
  category?: string;
  priority: InterventionPriority;
  equipment?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Budget {
  id: string;
  plantId: string;
  productionUnitId: string;
  year: number;
  status: BudgetStatus;
  version: number;
  maintenancePlanId?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  submittedAt?: Date;
  submittedBy?: string;
  approvedPlantAt?: Date;
  approvedPlantBy?: string;
  approvedHeadquartersAt?: Date;
  approvedHeadquartersBy?: string;
  rejectedAt?: Date;
  rejectedBy?: string;
  rejectionReason?: string;
  setReferenceAt?: Date;
  setReferenceBy?: string;
  plant?: Plant;
  productionUnit?: ProductionUnit;
  maintenancePlan?: MaintenancePlan;
  items?: BudgetItem[];
}

export interface BudgetItem {
  id: string;
  budgetId: string;
  kpiId: string;
  month: number;
  proposedValue?: number;
  budgetValue?: number;
  createdAt: Date;
  updatedAt: Date;
  kpi?: KPI;
}

export interface BudgetHistory {
  id: string;
  budgetId: string;
  action: string;
  oldStatus?: BudgetStatus;
  newStatus?: BudgetStatus;
  changedBy: string;
  changeReason?: string;
  changedAt: Date;
  metadata?: Record<string, any>;
}

// API Request/Response types
export interface CreateBudgetRequest {
  plantId: string;
  productionUnitId: string;
  year: number;
  maintenancePlanId?: string;
}

export interface UpdateBudgetRequest {
  maintenancePlanId?: string;
  items?: Array<{
    kpiId: string;
    month: number;
    proposedValue?: number;
    budgetValue?: number;
  }>;
}

export interface BudgetFilters {
  year?: number;
  plantId?: string;
  status?: BudgetStatus;
  createdBy?: string;
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

// Authentication types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: Omit<User, 'passwordHash'>;
  token: string;
  expiresIn: string;
}

export interface JWTPayload {
  userId: string;
  username: string;
  userType: UserType;
  iat: number;
  exp: number;
}

// Calculation types
export interface CalculationResult {
  success: boolean;
  calculatedItems: number;
  errors: string[];
  warnings: string[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
