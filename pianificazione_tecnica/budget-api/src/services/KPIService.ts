import { KPI, KPICategory } from '@/types';
import { executeQuery, executeQueryOne } from '@/database/connection';

export class KPIService {
  
  async getAllKPIs(): Promise<KPI[]> {
    const query = `
      SELECT 
        k.*,
        kc.name as category_name,
        kc.color as category_color,
        kc.display_order as category_display_order
      FROM kpis k
      LEFT JOIN kpi_categories kc ON k.category_id = kc.id
      WHERE k.is_active = true
      ORDER BY k.display_order, k.name
    `;
    
    const rows = await executeQuery(query);
    return rows.map(this.mapRowToKPI);
  }

  async getKPIById(id: string): Promise<KPI | null> {
    const query = `
      SELECT 
        k.*,
        kc.name as category_name,
        kc.color as category_color,
        kc.display_order as category_display_order
      FROM kpis k
      LEFT JOIN kpi_categories kc ON k.category_id = kc.id
      WHERE k.id = $1 AND k.is_active = true
    `;
    
    const row = await executeQueryOne(query, [id]);
    return row ? this.mapRowToKPI(row) : null;
  }

  async getAllKPICategories(): Promise<KPICategory[]> {
    const query = `
      SELECT * FROM kpi_categories
      ORDER BY display_order, name
    `;
    
    const rows = await executeQuery(query);
    return rows.map(this.mapRowToKPICategory);
  }

  async getKPICategoryById(id: string): Promise<KPICategory | null> {
    const query = 'SELECT * FROM kpi_categories WHERE id = $1';
    const row = await executeQueryOne(query, [id]);
    return row ? this.mapRowToKPICategory(row) : null;
  }

  async createKPI(data: Partial<KPI>): Promise<KPI> {
    const query = `
      INSERT INTO kpis (
        code, name, unit_of_measure, category_id, 
        is_auto_calculated, calculation_formula, display_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;
    
    const params = [
      data.code,
      data.name,
      data.unitOfMeasure,
      data.categoryId,
      data.isAutoCalculated || false,
      data.calculationFormula,
      data.displayOrder
    ];

    const rows = await executeQuery(query, params);
    const kpi = this.mapRowToKPI(rows[0]);
    
    // Load category info
    if (kpi.categoryId) {
      kpi.category = await this.getKPICategoryById(kpi.categoryId);
    }
    
    return kpi;
  }

  async updateKPI(id: string, data: Partial<KPI>): Promise<KPI> {
    const fields = [];
    const params = [];
    let paramIndex = 1;

    // Build SET clause dynamically
    if (data.code !== undefined) {
      fields.push(`code = $${paramIndex++}`);
      params.push(data.code);
    }
    if (data.name !== undefined) {
      fields.push(`name = $${paramIndex++}`);
      params.push(data.name);
    }
    if (data.unitOfMeasure !== undefined) {
      fields.push(`unit_of_measure = $${paramIndex++}`);
      params.push(data.unitOfMeasure);
    }
    if (data.categoryId !== undefined) {
      fields.push(`category_id = $${paramIndex++}`);
      params.push(data.categoryId);
    }
    if (data.isAutoCalculated !== undefined) {
      fields.push(`is_auto_calculated = $${paramIndex++}`);
      params.push(data.isAutoCalculated);
    }
    if (data.calculationFormula !== undefined) {
      fields.push(`calculation_formula = $${paramIndex++}`);
      params.push(data.calculationFormula);
    }
    if (data.displayOrder !== undefined) {
      fields.push(`display_order = $${paramIndex++}`);
      params.push(data.displayOrder);
    }

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    params.push(id);

    const query = `
      UPDATE kpis 
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex} AND is_active = true
      RETURNING *
    `;

    const rows = await executeQuery(query, params);
    if (rows.length === 0) {
      throw new Error('KPI not found or inactive');
    }

    return this.getKPIById(id) as Promise<KPI>;
  }

  async deleteKPI(id: string): Promise<void> {
    // Soft delete by setting is_active to false
    const query = `
      UPDATE kpis 
      SET is_active = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND is_active = true
    `;
    
    const rows = await executeQuery(query, [id]);
    if (rows.length === 0) {
      throw new Error('KPI not found or already inactive');
    }
  }

  async createKPICategory(data: Partial<KPICategory>): Promise<KPICategory> {
    const query = `
      INSERT INTO kpi_categories (name, color, display_order)
      VALUES ($1, $2, $3)
      RETURNING *
    `;
    
    const params = [data.name, data.color, data.displayOrder];
    const rows = await executeQuery(query, params);
    return this.mapRowToKPICategory(rows[0]);
  }

  async updateKPICategory(id: string, data: Partial<KPICategory>): Promise<KPICategory> {
    const fields = [];
    const params = [];
    let paramIndex = 1;

    if (data.name !== undefined) {
      fields.push(`name = $${paramIndex++}`);
      params.push(data.name);
    }
    if (data.color !== undefined) {
      fields.push(`color = $${paramIndex++}`);
      params.push(data.color);
    }
    if (data.displayOrder !== undefined) {
      fields.push(`display_order = $${paramIndex++}`);
      params.push(data.displayOrder);
    }

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    params.push(id);

    const query = `
      UPDATE kpi_categories 
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const rows = await executeQuery(query, params);
    if (rows.length === 0) {
      throw new Error('KPI Category not found');
    }

    return this.mapRowToKPICategory(rows[0]);
  }

  async deleteKPICategory(id: string): Promise<void> {
    // Check if category is used by any KPIs
    const usageCheck = await executeQueryOne(
      'SELECT COUNT(*) as count FROM kpis WHERE category_id = $1 AND is_active = true',
      [id]
    );

    if (usageCheck && parseInt(usageCheck.count) > 0) {
      throw new Error('Cannot delete category that is used by active KPIs');
    }

    const query = 'DELETE FROM kpi_categories WHERE id = $1';
    const rows = await executeQuery(query, [id]);
    if (rows.length === 0) {
      throw new Error('KPI Category not found');
    }
  }

  private mapRowToKPI(row: any): KPI {
    const kpi: KPI = {
      id: row.id,
      code: row.code,
      name: row.name,
      unitOfMeasure: row.unit_of_measure,
      categoryId: row.category_id,
      isAutoCalculated: row.is_auto_calculated,
      calculationFormula: row.calculation_formula,
      displayOrder: row.display_order,
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };

    // Add category info if available
    if (row.category_name) {
      kpi.category = {
        id: row.category_id,
        name: row.category_name,
        color: row.category_color,
        displayOrder: row.category_display_order,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }

    return kpi;
  }

  private mapRowToKPICategory(row: any): KPICategory {
    return {
      id: row.id,
      name: row.name,
      color: row.color,
      displayOrder: row.display_order,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }
}
