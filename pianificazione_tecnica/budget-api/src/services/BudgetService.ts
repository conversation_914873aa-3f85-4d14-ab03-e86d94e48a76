import { PoolClient } from 'pg';
import { 
  Budget, 
  BudgetItem, 
  BudgetFilters, 
  CreateBudgetRequest, 
  UpdateBudgetRequest,
  BudgetStatus,
  PaginatedResponse,
  ValidationResult,
  CalculationResult
} from '@/types';
import { executeQuery, executeQueryOne, withTransaction } from '@/database/connection';
import { logger, logBudgetAction } from '@/utils/logger';
import { BudgetRepository } from '@/repositories/BudgetRepository';
import { KPIService } from './KPIService';
import { CalculationService } from './CalculationService';

export class BudgetService {
  private budgetRepo: BudgetRepository;
  private kpiService: KPIService;
  private calculationService: CalculationService;

  constructor() {
    this.budgetRepo = new BudgetRepository();
    this.kpiService = new KPIService();
    this.calculationService = new CalculationService();
  }

  async createBudget(data: CreateBudgetRequest, userId: string): Promise<Budget> {
    return withTransaction(async (client: PoolClient) => {
      // Validate input
      await this.validateCreateBudgetRequest(data);

      // Check if budget already exists for this plant/unit/year
      const existingBudget = await this.budgetRepo.findByPlantUnitYear(
        data.plantId,
        data.productionUnitId,
        data.year,
        client
      );

      if (existingBudget) {
        throw new Error(`Budget already exists for this plant/unit/year combination`);
      }

      // Create budget
      const budget = await this.budgetRepo.create({
        ...data,
        status: BudgetStatus.DRAFT,
        version: 1,
        createdBy: userId,
      }, client);

      // Initialize budget items with all KPIs
      await this.initializeBudgetItems(budget.id, client);

      // Log action
      logBudgetAction('CREATE', budget.id, userId, { 
        plantId: data.plantId, 
        year: data.year 
      });

      // Return budget with items
      return this.getBudgetById(budget.id);
    });
  }

  async getBudgetById(id: string): Promise<Budget> {
    const budget = await this.budgetRepo.findByIdWithRelations(id);
    if (!budget) {
      throw new Error(`Budget with id ${id} not found`);
    }
    return budget;
  }

  async getBudgets(filters: BudgetFilters, userId: string): Promise<PaginatedResponse<Budget>> {
    // Apply user access filters based on user type
    const userFilters = await this.applyUserAccessFilters(filters, userId);
    
    return this.budgetRepo.findWithFilters(userFilters);
  }

  async updateBudget(id: string, data: UpdateBudgetRequest, userId: string): Promise<Budget> {
    return withTransaction(async (client: PoolClient) => {
      // Get existing budget
      const existingBudget = await this.budgetRepo.findById(id, client);
      if (!existingBudget) {
        throw new Error(`Budget with id ${id} not found`);
      }

      // Check if budget can be modified
      if (!this.canModifyBudget(existingBudget.status)) {
        throw new Error(`Budget in status ${existingBudget.status} cannot be modified`);
      }

      // Update budget basic info
      const updatedBudget = await this.budgetRepo.update(id, {
        maintenancePlanId: data.maintenancePlanId,
      }, client);

      // Update budget items if provided
      if (data.items && data.items.length > 0) {
        await this.updateBudgetItems(id, data.items, client);
      }

      // Log action
      logBudgetAction('UPDATE', id, userId, { 
        itemsUpdated: data.items?.length || 0 
      });

      return this.getBudgetById(id);
    });
  }

  async deleteBudget(id: string, userId: string): Promise<void> {
    return withTransaction(async (client: PoolClient) => {
      const budget = await this.budgetRepo.findById(id, client);
      if (!budget) {
        throw new Error(`Budget with id ${id} not found`);
      }

      // Only allow deletion of draft budgets
      if (budget.status !== BudgetStatus.DRAFT) {
        throw new Error(`Cannot delete budget in status ${budget.status}`);
      }

      await this.budgetRepo.delete(id, client);

      logBudgetAction('DELETE', id, userId);
    });
  }

  async submitBudget(id: string, userId: string): Promise<Budget> {
    return this.updateBudgetStatus(id, BudgetStatus.SUBMITTED, userId, 'Budget submitted for approval');
  }

  async approveBudgetAtPlant(id: string, userId: string): Promise<Budget> {
    return this.updateBudgetStatus(id, BudgetStatus.APPROVED_PLANT, userId, 'Budget approved at plant level');
  }

  async approveBudgetAtHeadquarters(id: string, userId: string): Promise<Budget> {
    return this.updateBudgetStatus(id, BudgetStatus.APPROVED_HEADQUARTERS, userId, 'Budget approved at headquarters level');
  }

  async rejectBudget(id: string, reason: string, userId: string): Promise<Budget> {
    return withTransaction(async (client: PoolClient) => {
      const budget = await this.budgetRepo.findById(id, client);
      if (!budget) {
        throw new Error(`Budget with id ${id} not found`);
      }

      const updatedBudget = await this.budgetRepo.update(id, {
        status: BudgetStatus.REJECTED,
        rejectedAt: new Date(),
        rejectedBy: userId,
        rejectionReason: reason,
      }, client);

      // Log action
      logBudgetAction('REJECT', id, userId, { reason });

      return this.getBudgetById(id);
    });
  }

  async setAsReference(id: string, userId: string): Promise<Budget> {
    return withTransaction(async (client: PoolClient) => {
      const budget = await this.budgetRepo.findById(id, client);
      if (!budget) {
        throw new Error(`Budget with id ${id} not found`);
      }

      if (budget.status !== BudgetStatus.APPROVED_HEADQUARTERS) {
        throw new Error('Only headquarters-approved budgets can be set as reference');
      }

      // Remove reference status from other budgets of the same plant/year
      await this.budgetRepo.clearReferenceStatus(budget.plantId, budget.year, client);

      // Set this budget as reference
      const updatedBudget = await this.budgetRepo.update(id, {
        status: BudgetStatus.REFERENCE,
        setReferenceAt: new Date(),
        setReferenceBy: userId,
      }, client);

      logBudgetAction('SET_REFERENCE', id, userId);

      return this.getBudgetById(id);
    });
  }

  async copyFromProposal(id: string, userId: string): Promise<CalculationResult> {
    return withTransaction(async (client: PoolClient) => {
      const budget = await this.budgetRepo.findById(id, client);
      if (!budget) {
        throw new Error(`Budget with id ${id} not found`);
      }

      const result = await this.calculationService.copyFromProposal(id, client);

      logBudgetAction('COPY_FROM_PROPOSAL', id, userId, result);

      return result;
    });
  }

  async calculateAutomatic(id: string, userId: string): Promise<CalculationResult> {
    return withTransaction(async (client: PoolClient) => {
      const budget = await this.budgetRepo.findById(id, client);
      if (!budget) {
        throw new Error(`Budget with id ${id} not found`);
      }

      const result = await this.calculationService.calculateAutomatic(id, client);

      logBudgetAction('CALCULATE_AUTOMATIC', id, userId, result);

      return result;
    });
  }

  async calculateDerived(id: string, userId: string): Promise<CalculationResult> {
    return withTransaction(async (client: PoolClient) => {
      const budget = await this.budgetRepo.findById(id, client);
      if (!budget) {
        throw new Error(`Budget with id ${id} not found`);
      }

      const result = await this.calculationService.calculateDerived(id, client);

      logBudgetAction('CALCULATE_DERIVED', id, userId, result);

      return result;
    });
  }

  async validateBudget(id: string): Promise<ValidationResult> {
    const budget = await this.getBudgetById(id);
    return this.calculationService.validateBudget(budget);
  }

  // Private helper methods

  private async validateCreateBudgetRequest(data: CreateBudgetRequest): Promise<void> {
    // Validate plant exists
    const plant = await executeQueryOne(
      'SELECT id FROM plants WHERE id = $1 AND is_active = true',
      [data.plantId]
    );
    if (!plant) {
      throw new Error('Invalid or inactive plant');
    }

    // Validate production unit exists and belongs to plant
    const unit = await executeQueryOne(
      'SELECT id FROM production_units WHERE id = $1 AND plant_id = $2 AND is_active = true',
      [data.productionUnitId, data.plantId]
    );
    if (!unit) {
      throw new Error('Invalid production unit or does not belong to specified plant');
    }

    // Validate year
    const currentYear = new Date().getFullYear();
    if (data.year < currentYear || data.year > currentYear + 5) {
      throw new Error('Year must be between current year and 5 years in the future');
    }

    // Validate maintenance plan if provided
    if (data.maintenancePlanId) {
      const plan = await executeQueryOne(
        'SELECT id FROM maintenance_plans WHERE id = $1 AND plant_id = $2 AND year = $3',
        [data.maintenancePlanId, data.plantId, data.year]
      );
      if (!plan) {
        throw new Error('Invalid maintenance plan or does not match plant/year');
      }
    }
  }

  private async initializeBudgetItems(budgetId: string, client: PoolClient): Promise<void> {
    // Get all active KPIs
    const kpis = await executeQuery(
      'SELECT id FROM kpis WHERE is_active = true ORDER BY display_order',
      [],
      'initializeBudgetItems'
    );

    // Create budget items for all months (1-12) and all KPIs
    const items = [];
    for (const kpi of kpis) {
      for (let month = 1; month <= 12; month++) {
        items.push({
          budgetId,
          kpiId: kpi.id,
          month,
        });
      }
    }

    // Batch insert
    if (items.length > 0) {
      const values = items.map((_, index) => 
        `($${index * 3 + 1}, $${index * 3 + 2}, $${index * 3 + 3})`
      ).join(', ');
      
      const params = items.flatMap(item => [item.budgetId, item.kpiId, item.month]);
      
      await client.query(
        `INSERT INTO budget_items (budget_id, kpi_id, month) VALUES ${values}`,
        params
      );
    }
  }

  private async updateBudgetItems(
    budgetId: string, 
    items: UpdateBudgetRequest['items'], 
    client: PoolClient
  ): Promise<void> {
    if (!items || items.length === 0) return;

    for (const item of items) {
      await client.query(
        `UPDATE budget_items 
         SET proposed_value = $1, budget_value = $2, updated_at = CURRENT_TIMESTAMP
         WHERE budget_id = $3 AND kpi_id = $4 AND month = $5`,
        [item.proposedValue, item.budgetValue, budgetId, item.kpiId, item.month]
      );
    }
  }

  private async updateBudgetStatus(
    id: string, 
    status: BudgetStatus, 
    userId: string, 
    reason: string
  ): Promise<Budget> {
    return withTransaction(async (client: PoolClient) => {
      const budget = await this.budgetRepo.findById(id, client);
      if (!budget) {
        throw new Error(`Budget with id ${id} not found`);
      }

      // Validate status transition
      if (!this.isValidStatusTransition(budget.status, status)) {
        throw new Error(`Invalid status transition from ${budget.status} to ${status}`);
      }

      // Update budget with new status and timestamp
      const updateData: any = { status };
      const now = new Date();

      switch (status) {
        case BudgetStatus.SUBMITTED:
          updateData.submittedAt = now;
          updateData.submittedBy = userId;
          break;
        case BudgetStatus.APPROVED_PLANT:
          updateData.approvedPlantAt = now;
          updateData.approvedPlantBy = userId;
          break;
        case BudgetStatus.APPROVED_HEADQUARTERS:
          updateData.approvedHeadquartersAt = now;
          updateData.approvedHeadquartersBy = userId;
          break;
      }

      await this.budgetRepo.update(id, updateData, client);

      // Add to history
      await this.budgetRepo.addHistory(id, {
        action: status,
        oldStatus: budget.status,
        newStatus: status,
        changedBy: userId,
        changeReason: reason,
      }, client);

      logBudgetAction(status, id, userId, { reason });

      return this.getBudgetById(id);
    });
  }

  private canModifyBudget(status: BudgetStatus): boolean {
    return status === BudgetStatus.DRAFT || status === BudgetStatus.REJECTED;
  }

  private isValidStatusTransition(from: BudgetStatus, to: BudgetStatus): boolean {
    const validTransitions: Record<BudgetStatus, BudgetStatus[]> = {
      [BudgetStatus.DRAFT]: [BudgetStatus.SUBMITTED],
      [BudgetStatus.SUBMITTED]: [BudgetStatus.APPROVED_PLANT, BudgetStatus.REJECTED],
      [BudgetStatus.APPROVED_PLANT]: [BudgetStatus.APPROVED_HEADQUARTERS, BudgetStatus.REJECTED],
      [BudgetStatus.APPROVED_HEADQUARTERS]: [BudgetStatus.REFERENCE],
      [BudgetStatus.REFERENCE]: [],
      [BudgetStatus.REJECTED]: [BudgetStatus.SUBMITTED],
    };

    return validTransitions[from]?.includes(to) || false;
  }

  private async applyUserAccessFilters(filters: BudgetFilters, userId: string): Promise<BudgetFilters> {
    // Get user info to apply access restrictions
    const user = await executeQueryOne(
      'SELECT user_type, has_access_to_thermal, has_access_to_hydroelectric FROM users WHERE id = $1',
      [userId]
    );

    if (!user) {
      throw new Error('User not found');
    }

    // Apply plant type restrictions based on user access
    const modifiedFilters = { ...filters };

    // Add plant type filter if user doesn't have access to all types
    if (!user.has_access_to_thermal || !user.has_access_to_hydroelectric) {
      const allowedPlantTypes = [];
      if (user.has_access_to_thermal) allowedPlantTypes.push('THERMAL');
      if (user.has_access_to_hydroelectric) allowedPlantTypes.push('HYDROELECTRIC');
      
      modifiedFilters.plantTypes = allowedPlantTypes;
    }

    return modifiedFilters;
  }
}
