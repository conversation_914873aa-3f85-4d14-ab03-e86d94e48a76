import { PoolClient } from 'pg';
import { Budget, CalculationResult, ValidationResult } from '@/types';
import { executeQuery } from '@/database/connection';

export class CalculationService {

  async copyFromProposal(budgetId: string, client?: PoolClient): Promise<CalculationResult> {
    const result: CalculationResult = {
      success: false,
      calculatedItems: 0,
      errors: [],
      warnings: [],
    };

    try {
      const query = `
        UPDATE budget_items 
        SET budget_value = proposed_value, updated_at = CURRENT_TIMESTAMP
        WHERE budget_id = $1 AND proposed_value IS NOT NULL
      `;

      const executor = client ? client.query.bind(client) : executeQuery;
      const updateResult = await executor(query, [budgetId]);
      
      result.calculatedItems = updateResult.rowCount || 0;
      result.success = true;

      if (result.calculatedItems === 0) {
        result.warnings.push('No proposed values found to copy');
      }

    } catch (error) {
      result.errors.push(`Error copying from proposal: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  async calculateAutomatic(budgetId: string, client?: PoolClient): Promise<CalculationResult> {
    const result: CalculationResult = {
      success: false,
      calculatedItems: 0,
      errors: [],
      warnings: [],
    };

    try {
      // Get all auto-calculated KPIs for this budget
      const autoKPIsQuery = `
        SELECT DISTINCT k.id, k.code, k.calculation_formula
        FROM kpis k
        INNER JOIN budget_items bi ON k.id = bi.kpi_id
        WHERE bi.budget_id = $1 AND k.is_auto_calculated = true AND k.calculation_formula IS NOT NULL
        ORDER BY k.display_order
      `;

      const executor = client ? client.query.bind(client) : executeQuery;
      const autoKPIs = await executor(autoKPIsQuery, [budgetId]);

      for (const kpi of autoKPIs) {
        try {
          const calculatedCount = await this.calculateKPIValues(budgetId, kpi, client);
          result.calculatedItems += calculatedCount;
        } catch (error) {
          result.errors.push(`Error calculating KPI ${kpi.code}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      result.success = result.errors.length === 0;

      if (autoKPIs.length === 0) {
        result.warnings.push('No auto-calculated KPIs found');
      }

    } catch (error) {
      result.errors.push(`Error in automatic calculation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  async calculateDerived(budgetId: string, client?: PoolClient): Promise<CalculationResult> {
    const result: CalculationResult = {
      success: false,
      calculatedItems: 0,
      errors: [],
      warnings: [],
    };

    try {
      // Get all KPIs with formulas that reference other KPIs
      const derivedKPIsQuery = `
        SELECT DISTINCT k.id, k.code, k.calculation_formula
        FROM kpis k
        INNER JOIN budget_items bi ON k.id = bi.kpi_id
        WHERE bi.budget_id = $1 
          AND k.calculation_formula IS NOT NULL 
          AND k.calculation_formula ~ '[A-Z_]+_[0-9]+'
        ORDER BY k.display_order
      `;

      const executor = client ? client.query.bind(client) : executeQuery;
      const derivedKPIs = await executor(derivedKPIsQuery, [budgetId]);

      for (const kpi of derivedKPIs) {
        try {
          const calculatedCount = await this.calculateKPIValues(budgetId, kpi, client);
          result.calculatedItems += calculatedCount;
        } catch (error) {
          result.errors.push(`Error calculating derived KPI ${kpi.code}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      result.success = result.errors.length === 0;

      if (derivedKPIs.length === 0) {
        result.warnings.push('No derived KPIs found');
      }

    } catch (error) {
      result.errors.push(`Error in derived calculation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  async validateBudget(budget: Budget): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      // Check for missing required values
      if (!budget.items || budget.items.length === 0) {
        result.errors.push('Budget has no items');
        result.isValid = false;
        return result;
      }

      // Check for negative values where not allowed
      const negativeValues = budget.items.filter(item => 
        (item.proposedValue !== undefined && item.proposedValue < 0) ||
        (item.budgetValue !== undefined && item.budgetValue < 0)
      );

      if (negativeValues.length > 0) {
        result.warnings.push(`Found ${negativeValues.length} items with negative values`);
      }

      // Check for extremely large values
      const maxValue = 999999999;
      const largeValues = budget.items.filter(item =>
        (item.proposedValue !== undefined && item.proposedValue > maxValue) ||
        (item.budgetValue !== undefined && item.budgetValue > maxValue)
      );

      if (largeValues.length > 0) {
        result.errors.push(`Found ${largeValues.length} items with values exceeding maximum allowed (${maxValue})`);
        result.isValid = false;
      }

      // Check for missing budget values in submitted budgets
      if (budget.status !== 'DRAFT') {
        const missingBudgetValues = budget.items.filter(item => 
          item.budgetValue === undefined || item.budgetValue === null
        );

        if (missingBudgetValues.length > 0) {
          result.warnings.push(`Found ${missingBudgetValues.length} items without budget values`);
        }
      }

      // Validate formula consistency for auto-calculated KPIs
      const autoCalculatedItems = budget.items.filter(item => 
        item.kpi?.isAutoCalculated && item.kpi?.calculationFormula
      );

      for (const item of autoCalculatedItems) {
        try {
          // This is a simplified validation - in a real implementation,
          // you would parse and validate the formula more thoroughly
          if (item.kpi?.calculationFormula && !this.isValidFormula(item.kpi.calculationFormula)) {
            result.warnings.push(`KPI ${item.kpi.code} has an invalid calculation formula`);
          }
        } catch (error) {
          result.warnings.push(`Error validating formula for KPI ${item.kpi?.code}`);
        }
      }

    } catch (error) {
      result.errors.push(`Error during validation: ${error instanceof Error ? error.message : 'Unknown error'}`);
      result.isValid = false;
    }

    return result;
  }

  private async calculateKPIValues(budgetId: string, kpi: any, client?: PoolClient): Promise<number> {
    let calculatedCount = 0;

    // Get all months for this KPI
    for (let month = 1; month <= 12; month++) {
      try {
        const proposedValue = await this.evaluateFormula(kpi.calculation_formula, budgetId, kpi.id, month, 'proposed_value', client);
        const budgetValue = await this.evaluateFormula(kpi.calculation_formula, budgetId, kpi.id, month, 'budget_value', client);

        // Update the budget item
        const updateQuery = `
          UPDATE budget_items 
          SET proposed_value = $1, budget_value = $2, updated_at = CURRENT_TIMESTAMP
          WHERE budget_id = $3 AND kpi_id = $4 AND month = $5
        `;

        const executor = client ? client.query.bind(client) : executeQuery;
        await executor(updateQuery, [proposedValue, budgetValue, budgetId, kpi.id, month]);
        calculatedCount++;

      } catch (error) {
        // Log error but continue with other months
        console.error(`Error calculating KPI ${kpi.code} for month ${month}:`, error);
      }
    }

    return calculatedCount;
  }

  private async evaluateFormula(
    formula: string, 
    budgetId: string, 
    kpiId: string, 
    month: number, 
    valueType: 'proposed_value' | 'budget_value',
    client?: PoolClient
  ): Promise<number | null> {
    try {
      let evaluatedFormula = formula;

      // Find all KPI references in the formula (pattern: KPI_CODE_123)
      const kpiReferences = formula.match(/[A-Z_]+_\d+/g) || [];

      // Replace each KPI reference with its actual value
      for (const kpiCode of kpiReferences) {
        const valueQuery = `
          SELECT bi.${valueType}
          FROM budget_items bi
          INNER JOIN kpis k ON bi.kpi_id = k.id
          WHERE bi.budget_id = $1 AND k.code = $2 AND bi.month = $3
        `;

        const executor = client ? client.query.bind(client) : executeQuery;
        const valueResult = await executor(valueQuery, [budgetId, kpiCode, month]);
        
        const value = valueResult.length > 0 ? (valueResult[0][valueType] || 0) : 0;
        evaluatedFormula = evaluatedFormula.replace(new RegExp(kpiCode, 'g'), value.toString());
      }

      // Evaluate the mathematical expression safely
      return this.safeEval(evaluatedFormula);

    } catch (error) {
      console.error('Error evaluating formula:', formula, error);
      return null;
    }
  }

  private safeEval(expression: string): number | null {
    try {
      // Remove whitespace and validate that expression contains only safe characters
      const cleanExpression = expression.replace(/\s/g, '');
      
      if (!/^[0-9+\-*/.()]+$/.test(cleanExpression)) {
        throw new Error('Invalid characters in expression');
      }

      // Use Function constructor for safer evaluation than eval()
      const result = new Function(`return ${cleanExpression}`)();
      
      return typeof result === 'number' && !isNaN(result) ? result : null;
    } catch (error) {
      console.error('Error in safe eval:', expression, error);
      return null;
    }
  }

  private isValidFormula(formula: string): boolean {
    try {
      // Basic validation - check for balanced parentheses
      let parenthesesCount = 0;
      for (const char of formula) {
        if (char === '(') parenthesesCount++;
        if (char === ')') parenthesesCount--;
        if (parenthesesCount < 0) return false;
      }
      
      if (parenthesesCount !== 0) return false;

      // Check for valid characters (letters, numbers, operators, parentheses, underscore)
      if (!/^[A-Za-z0-9+\-*/.()_\s]+$/.test(formula)) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }
}
