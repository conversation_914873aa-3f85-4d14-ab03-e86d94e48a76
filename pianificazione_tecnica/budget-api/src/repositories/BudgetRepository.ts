import { PoolClient } from 'pg';
import { Budget, BudgetFilters, BudgetStatus, PaginatedResponse, BudgetHistory } from '@/types';
import { executeQuery, executeQueryOne } from '@/database/connection';

export class BudgetRepository {
  
  async create(data: Partial<Budget>, client?: PoolClient): Promise<Budget> {
    const query = `
      INSERT INTO budgets (
        plant_id, production_unit_id, year, status, version, 
        maintenance_plan_id, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;
    
    const params = [
      data.plantId,
      data.productionUnitId,
      data.year,
      data.status,
      data.version,
      data.maintenancePlanId,
      data.createdBy
    ];

    const executor = client ? client.query.bind(client) : executeQuery;
    const rows = await executor(query, params);
    return this.mapRowToBudget(rows[0]);
  }

  async findById(id: string, client?: PoolClient): Promise<Budget | null> {
    const query = 'SELECT * FROM budgets WHERE id = $1';
    const executor = client ? client.query.bind(client) : executeQueryOne;
    const row = await executor(query, [id]);
    return row ? this.mapRowToBudget(row) : null;
  }

  async findByIdWithRelations(id: string): Promise<Budget | null> {
    const query = `
      SELECT 
        b.*,
        p.code as plant_code, p.name as plant_name, p.type as plant_type,
        pu.code as unit_code, pu.name as unit_name,
        mp.code as maintenance_plan_code, mp.name as maintenance_plan_name
      FROM budgets b
      LEFT JOIN plants p ON b.plant_id = p.id
      LEFT JOIN production_units pu ON b.production_unit_id = pu.id
      LEFT JOIN maintenance_plans mp ON b.maintenance_plan_id = mp.id
      WHERE b.id = $1
    `;
    
    const row = await executeQueryOne(query, [id]);
    if (!row) return null;

    const budget = this.mapRowToBudget(row);
    
    // Add related entities
    if (row.plant_code) {
      budget.plant = {
        id: budget.plantId,
        code: row.plant_code,
        name: row.plant_name,
        type: row.plant_type,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }

    if (row.unit_code) {
      budget.productionUnit = {
        id: budget.productionUnitId,
        plantId: budget.plantId,
        code: row.unit_code,
        name: row.unit_name,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }

    if (row.maintenance_plan_code) {
      budget.maintenancePlan = {
        id: budget.maintenancePlanId!,
        code: row.maintenance_plan_code,
        name: row.maintenance_plan_name,
        year: budget.year,
        plantId: budget.plantId,
        status: 'REFERENCE' as any,
        type: 'PROGRAMMED' as any,
        totalCost: 0,
        isReference: true,
        createdBy: '',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }

    // Load budget items
    budget.items = await this.getBudgetItems(id);

    return budget;
  }

  async findByPlantUnitYear(
    plantId: string, 
    productionUnitId: string, 
    year: number,
    client?: PoolClient
  ): Promise<Budget | null> {
    const query = `
      SELECT * FROM budgets 
      WHERE plant_id = $1 AND production_unit_id = $2 AND year = $3
    `;
    const executor = client ? client.query.bind(client) : executeQueryOne;
    const row = await executor(query, [plantId, productionUnitId, year]);
    return row ? this.mapRowToBudget(row) : null;
  }

  async findWithFilters(filters: BudgetFilters): Promise<PaginatedResponse<Budget>> {
    const conditions = [];
    const params = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (filters.year) {
      conditions.push(`b.year = $${paramIndex++}`);
      params.push(filters.year);
    }

    if (filters.plantId) {
      conditions.push(`b.plant_id = $${paramIndex++}`);
      params.push(filters.plantId);
    }

    if (filters.status) {
      conditions.push(`b.status = $${paramIndex++}`);
      params.push(filters.status);
    }

    if (filters.createdBy) {
      conditions.push(`b.created_by = $${paramIndex++}`);
      params.push(filters.createdBy);
    }

    // Add plant type filter if specified
    if ((filters as any).plantTypes && (filters as any).plantTypes.length > 0) {
      const placeholders = (filters as any).plantTypes.map(() => `$${paramIndex++}`).join(', ');
      conditions.push(`p.type IN (${placeholders})`);
      params.push(...(filters as any).plantTypes);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Count total records
    const countQuery = `
      SELECT COUNT(*) as total
      FROM budgets b
      LEFT JOIN plants p ON b.plant_id = p.id
      ${whereClause}
    `;
    const countResult = await executeQueryOne(countQuery, params);
    const total = parseInt(countResult?.total || '0');

    // Calculate pagination
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const offset = (page - 1) * limit;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const dataQuery = `
      SELECT 
        b.*,
        p.code as plant_code, p.name as plant_name, p.type as plant_type,
        pu.code as unit_code, pu.name as unit_name
      FROM budgets b
      LEFT JOIN plants p ON b.plant_id = p.id
      LEFT JOIN production_units pu ON b.production_unit_id = pu.id
      ${whereClause}
      ORDER BY b.created_at DESC
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;
    params.push(limit, offset);

    const rows = await executeQuery(dataQuery, params);
    const budgets = rows.map(row => {
      const budget = this.mapRowToBudget(row);
      
      // Add basic plant and unit info
      if (row.plant_code) {
        budget.plant = {
          id: budget.plantId,
          code: row.plant_code,
          name: row.plant_name,
          type: row.plant_type,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      }

      if (row.unit_code) {
        budget.productionUnit = {
          id: budget.productionUnitId,
          plantId: budget.plantId,
          code: row.unit_code,
          name: row.unit_name,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      }

      return budget;
    });

    return {
      data: budgets,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    };
  }

  async update(id: string, data: Partial<Budget>, client?: PoolClient): Promise<Budget> {
    const fields = [];
    const params = [];
    let paramIndex = 1;

    // Build SET clause dynamically
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id') {
        const dbField = this.camelToSnake(key);
        fields.push(`${dbField} = $${paramIndex++}`);
        params.push(value);
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    params.push(id);

    const query = `
      UPDATE budgets 
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const executor = client ? client.query.bind(client) : executeQuery;
    const rows = await executor(query, params);
    return this.mapRowToBudget(rows[0]);
  }

  async delete(id: string, client?: PoolClient): Promise<void> {
    const query = 'DELETE FROM budgets WHERE id = $1';
    const executor = client ? client.query.bind(client) : executeQuery;
    await executor(query, [id]);
  }

  async clearReferenceStatus(plantId: string, year: number, client?: PoolClient): Promise<void> {
    const query = `
      UPDATE budgets 
      SET status = 'APPROVED_HEADQUARTERS', set_reference_at = NULL, set_reference_by = NULL
      WHERE plant_id = $1 AND year = $2 AND status = 'REFERENCE'
    `;
    const executor = client ? client.query.bind(client) : executeQuery;
    await executor(query, [plantId, year]);
  }

  async addHistory(budgetId: string, historyData: Partial<BudgetHistory>, client?: PoolClient): Promise<void> {
    const query = `
      INSERT INTO budget_history (
        budget_id, action, old_status, new_status, changed_by, change_reason, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `;
    
    const params = [
      budgetId,
      historyData.action,
      historyData.oldStatus,
      historyData.newStatus,
      historyData.changedBy,
      historyData.changeReason,
      historyData.metadata ? JSON.stringify(historyData.metadata) : null
    ];

    const executor = client ? client.query.bind(client) : executeQuery;
    await executor(query, params);
  }

  private async getBudgetItems(budgetId: string): Promise<any[]> {
    const query = `
      SELECT 
        bi.*,
        k.code as kpi_code, k.name as kpi_name, k.unit_of_measure,
        kc.name as category_name, kc.color as category_color
      FROM budget_items bi
      LEFT JOIN kpis k ON bi.kpi_id = k.id
      LEFT JOIN kpi_categories kc ON k.category_id = kc.id
      WHERE bi.budget_id = $1
      ORDER BY k.display_order, bi.month
    `;
    
    const rows = await executeQuery(query, [budgetId]);
    return rows.map(row => ({
      id: row.id,
      budgetId: row.budget_id,
      kpiId: row.kpi_id,
      month: row.month,
      proposedValue: row.proposed_value,
      budgetValue: row.budget_value,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      kpi: {
        id: row.kpi_id,
        code: row.kpi_code,
        name: row.kpi_name,
        unitOfMeasure: row.unit_of_measure,
        category: {
          name: row.category_name,
          color: row.category_color,
        },
      },
    }));
  }

  private mapRowToBudget(row: any): Budget {
    return {
      id: row.id,
      plantId: row.plant_id,
      productionUnitId: row.production_unit_id,
      year: row.year,
      status: row.status as BudgetStatus,
      version: row.version,
      maintenancePlanId: row.maintenance_plan_id,
      createdBy: row.created_by,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      submittedAt: row.submitted_at,
      submittedBy: row.submitted_by,
      approvedPlantAt: row.approved_plant_at,
      approvedPlantBy: row.approved_plant_by,
      approvedHeadquartersAt: row.approved_headquarters_at,
      approvedHeadquartersBy: row.approved_headquarters_by,
      rejectedAt: row.rejected_at,
      rejectedBy: row.rejected_by,
      rejectionReason: row.rejection_reason,
      setReferenceAt: row.set_reference_at,
      setReferenceBy: row.set_reference_by,
    };
  }

  private camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
