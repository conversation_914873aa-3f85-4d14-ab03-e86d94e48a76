import Joi from 'joi';

// Validation schema for creating a budget
export const createBudgetSchema = Joi.object({
  plantId: Joi.string().uuid().required().messages({
    'string.uuid': 'Plant ID must be a valid UUID',
    'any.required': 'Plant ID is required',
  }),
  productionUnitId: Joi.string().uuid().required().messages({
    'string.uuid': 'Production Unit ID must be a valid UUID',
    'any.required': 'Production Unit ID is required',
  }),
  year: Joi.number().integer().min(2020).max(2030).required().messages({
    'number.base': 'Year must be a number',
    'number.integer': 'Year must be an integer',
    'number.min': 'Year must be at least 2020',
    'number.max': 'Year must be at most 2030',
    'any.required': 'Year is required',
  }),
  maintenancePlanId: Joi.string().uuid().optional().messages({
    'string.uuid': 'Maintenance Plan ID must be a valid UUID',
  }),
});

// Validation schema for updating a budget
export const updateBudgetSchema = Joi.object({
  maintenancePlanId: Joi.string().uuid().optional().allow(null).messages({
    'string.uuid': 'Maintenance Plan ID must be a valid UUID',
  }),
  items: Joi.array().items(
    Joi.object({
      kpiId: Joi.string().uuid().required().messages({
        'string.uuid': 'KPI ID must be a valid UUID',
        'any.required': 'KPI ID is required',
      }),
      month: Joi.number().integer().min(1).max(12).required().messages({
        'number.base': 'Month must be a number',
        'number.integer': 'Month must be an integer',
        'number.min': 'Month must be at least 1',
        'number.max': 'Month must be at most 12',
        'any.required': 'Month is required',
      }),
      proposedValue: Joi.number().optional().allow(null).messages({
        'number.base': 'Proposed value must be a number',
      }),
      budgetValue: Joi.number().optional().allow(null).messages({
        'number.base': 'Budget value must be a number',
      }),
    })
  ).optional().messages({
    'array.base': 'Items must be an array',
  }),
}).min(1).messages({
  'object.min': 'At least one field must be provided for update',
});

// Validation schema for budget filters
export const budgetFiltersSchema = Joi.object({
  year: Joi.number().integer().min(2020).max(2030).optional().messages({
    'number.base': 'Year must be a number',
    'number.integer': 'Year must be an integer',
    'number.min': 'Year must be at least 2020',
    'number.max': 'Year must be at most 2030',
  }),
  plantId: Joi.string().uuid().optional().messages({
    'string.uuid': 'Plant ID must be a valid UUID',
  }),
  status: Joi.string().valid(
    'DRAFT',
    'SUBMITTED', 
    'APPROVED_PLANT',
    'APPROVED_HEADQUARTERS',
    'REFERENCE',
    'REJECTED'
  ).optional().messages({
    'any.only': 'Status must be one of: DRAFT, SUBMITTED, APPROVED_PLANT, APPROVED_HEADQUARTERS, REFERENCE, REJECTED',
  }),
  createdBy: Joi.string().uuid().optional().messages({
    'string.uuid': 'Created By must be a valid UUID',
  }),
  page: Joi.number().integer().min(1).optional().default(1).messages({
    'number.base': 'Page must be a number',
    'number.integer': 'Page must be an integer',
    'number.min': 'Page must be at least 1',
  }),
  limit: Joi.number().integer().min(1).max(100).optional().default(10).messages({
    'number.base': 'Limit must be a number',
    'number.integer': 'Limit must be an integer',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit must be at most 100',
  }),
});

// Validation schema for KPI creation
export const createKPISchema = Joi.object({
  code: Joi.string().min(3).max(20).pattern(/^[A-Z_0-9]+$/).required().messages({
    'string.base': 'Code must be a string',
    'string.min': 'Code must be at least 3 characters long',
    'string.max': 'Code must be at most 20 characters long',
    'string.pattern.base': 'Code must contain only uppercase letters, numbers, and underscores',
    'any.required': 'Code is required',
  }),
  name: Joi.string().min(3).max(255).required().messages({
    'string.base': 'Name must be a string',
    'string.min': 'Name must be at least 3 characters long',
    'string.max': 'Name must be at most 255 characters long',
    'any.required': 'Name is required',
  }),
  unitOfMeasure: Joi.string().min(1).max(20).required().messages({
    'string.base': 'Unit of measure must be a string',
    'string.min': 'Unit of measure must be at least 1 character long',
    'string.max': 'Unit of measure must be at most 20 characters long',
    'any.required': 'Unit of measure is required',
  }),
  categoryId: Joi.string().uuid().required().messages({
    'string.uuid': 'Category ID must be a valid UUID',
    'any.required': 'Category ID is required',
  }),
  isAutoCalculated: Joi.boolean().optional().default(false).messages({
    'boolean.base': 'Is auto calculated must be a boolean',
  }),
  calculationFormula: Joi.string().max(1000).optional().allow(null, '').messages({
    'string.base': 'Calculation formula must be a string',
    'string.max': 'Calculation formula must be at most 1000 characters long',
  }),
  displayOrder: Joi.number().integer().min(1).required().messages({
    'number.base': 'Display order must be a number',
    'number.integer': 'Display order must be an integer',
    'number.min': 'Display order must be at least 1',
    'any.required': 'Display order is required',
  }),
});

// Validation schema for KPI category creation
export const createKPICategorySchema = Joi.object({
  name: Joi.string().min(3).max(100).required().messages({
    'string.base': 'Name must be a string',
    'string.min': 'Name must be at least 3 characters long',
    'string.max': 'Name must be at most 100 characters long',
    'any.required': 'Name is required',
  }),
  color: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).required().messages({
    'string.base': 'Color must be a string',
    'string.pattern.base': 'Color must be a valid hex color code (e.g., #FF0000)',
    'any.required': 'Color is required',
  }),
  displayOrder: Joi.number().integer().min(1).required().messages({
    'number.base': 'Display order must be a number',
    'number.integer': 'Display order must be an integer',
    'number.min': 'Display order must be at least 1',
    'any.required': 'Display order is required',
  }),
});

// Validation schema for user login
export const loginSchema = Joi.object({
  username: Joi.string().min(3).max(50).required().messages({
    'string.base': 'Username must be a string',
    'string.min': 'Username must be at least 3 characters long',
    'string.max': 'Username must be at most 50 characters long',
    'any.required': 'Username is required',
  }),
  password: Joi.string().min(6).required().messages({
    'string.base': 'Password must be a string',
    'string.min': 'Password must be at least 6 characters long',
    'any.required': 'Password is required',
  }),
});

// Helper functions to validate data
export const validateCreateBudget = (data: any) => {
  return createBudgetSchema.validate(data, { abortEarly: false });
};

export const validateUpdateBudget = (data: any) => {
  return updateBudgetSchema.validate(data, { abortEarly: false });
};

export const validateBudgetFilters = (data: any) => {
  return budgetFiltersSchema.validate(data, { abortEarly: false });
};

export const validateCreateKPI = (data: any) => {
  return createKPISchema.validate(data, { abortEarly: false });
};

export const validateCreateKPICategory = (data: any) => {
  return createKPICategorySchema.validate(data, { abortEarly: false });
};

export const validateLogin = (data: any) => {
  return loginSchema.validate(data, { abortEarly: false });
};

// Custom validation for UUID parameters
export const validateUUID = (value: string, fieldName: string = 'ID') => {
  const uuidSchema = Joi.string().uuid().required();
  const { error } = uuidSchema.validate(value);
  
  if (error) {
    throw new Error(`${fieldName} must be a valid UUID`);
  }
  
  return value;
};

// Custom validation for pagination parameters
export const validatePagination = (page?: string, limit?: string) => {
  const paginationSchema = Joi.object({
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(10),
  });
  
  const { error, value } = paginationSchema.validate({
    page: page ? parseInt(page) : undefined,
    limit: limit ? parseInt(limit) : undefined,
  });
  
  if (error) {
    throw new Error(`Invalid pagination parameters: ${error.message}`);
  }
  
  return value;
};
