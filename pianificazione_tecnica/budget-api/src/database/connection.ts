import { Pool, PoolClient, PoolConfig } from 'pg';
import { logger } from '../utils/logger';

class DatabaseConnection {
  private pool: Pool;
  private isConnected = false;

  constructor() {
    const config: PoolConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_NAME || 'budget_db',
      user: process.env.DB_USER || 'budget_user',
      password: process.env.DB_PASSWORD || 'budget_pass',
      ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
      max: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    };

    this.pool = new Pool(config);
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.pool.on('connect', (client: PoolClient) => {
      logger.info('New database client connected');
      this.isConnected = true;
    });

    this.pool.on('error', (err: Error) => {
      logger.error('Database pool error:', err);
      this.isConnected = false;
    });

    this.pool.on('remove', () => {
      logger.info('Database client removed from pool');
    });
  }

  async connect(): Promise<void> {
    try {
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();
      this.isConnected = true;
      logger.info('Database connection established successfully');
    } catch (error) {
      this.isConnected = false;
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.pool.end();
      this.isConnected = false;
      logger.info('Database connection closed');
    } catch (error) {
      logger.error('Error closing database connection:', error);
      throw error;
    }
  }

  async query<T = any>(text: string, params?: any[]): Promise<T[]> {
    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      
      logger.debug('Query executed', {
        query: text,
        duration: `${duration}ms`,
        rows: result.rowCount
      });

      return result.rows;
    } catch (error) {
      const duration = Date.now() - start;
      logger.error('Query failed', {
        query: text,
        params,
        duration: `${duration}ms`,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  async queryOne<T = any>(text: string, params?: any[]): Promise<T | null> {
    const rows = await this.query<T>(text, params);
    return rows.length > 0 ? rows[0] : null;
  }

  async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  async healthCheck(): Promise<{
    isConnected: boolean;
    poolSize: number;
    idleCount: number;
    waitingCount: number;
  }> {
    try {
      await this.query('SELECT 1');
      return {
        isConnected: this.isConnected,
        poolSize: this.pool.totalCount,
        idleCount: this.pool.idleCount,
        waitingCount: this.pool.waitingCount,
      };
    } catch (error) {
      return {
        isConnected: false,
        poolSize: 0,
        idleCount: 0,
        waitingCount: 0,
      };
    }
  }

  getPool(): Pool {
    return this.pool;
  }

  isHealthy(): boolean {
    return this.isConnected;
  }
}

// Singleton instance
export const db = new DatabaseConnection();

// Helper function for raw queries with better error handling
export async function executeQuery<T = any>(
  query: string,
  params?: any[],
  errorContext?: string
): Promise<T[]> {
  try {
    return await db.query<T>(query, params);
  } catch (error) {
    logger.error(`Database query failed${errorContext ? ` in ${errorContext}` : ''}`, {
      query,
      params,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

// Helper function for single row queries
export async function executeQueryOne<T = any>(
  query: string,
  params?: any[],
  errorContext?: string
): Promise<T | null> {
  const rows = await executeQuery<T>(query, params, errorContext);
  return rows.length > 0 ? rows[0] : null;
}

// Helper function for transactions
export async function withTransaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  return db.transaction(callback);
}
