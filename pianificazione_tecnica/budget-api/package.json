{"name": "budget-api", "version": "1.0.0", "description": "Modern API Backend for Budget Management System", "main": "dist/index.js", "scripts": {"dev": "npx tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "tsx src/database/migrate.ts", "db:seed": "tsx src/database/seed.ts", "db:reset": "tsx src/database/reset.ts"}, "keywords": ["budget", "api", "postgresql", "typescript", "express"], "author": "A2A Budget Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "pg": "^8.11.3", "pg-pool": "^3.6.1", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "winston": "^3.11.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "date-fns": "^2.30.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/pg": "^8.10.9", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@types/node": "^20.10.0", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.2", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}