# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=budget_db
DB_USER=budget_user
DB_PASSWORD=budget_pass
DB_SSL=false
DB_MAX_CONNECTIONS=20

# Server Configuration
PORT=3001
NODE_ENV=development
API_PREFIX=/api/v1

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# Logging
LOG_LEVEL=info
LOG_FILE=logs/budget-api.log

# CORS
CORS_ORIGIN=http://localhost:3002
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

# Email (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=Budget System <<EMAIL>>
