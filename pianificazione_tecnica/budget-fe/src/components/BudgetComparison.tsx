import React from 'react';
import { Modal, Table, Select, Card, Typography, Space, Tag, Button, Statistic, Row, Col } from 'antd';
import { CompareArrowsOutlined, BarChartOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { budgetApi } from '../services/budgetApi';
import { Budget, BudgetGridRow, BudgetStatus } from '../types/budget';
import { CalculationService } from '../services/calculationService';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;

interface BudgetComparisonProps {
  currentBudget: Budget;
  visible: boolean;
  onClose: () => void;
}

interface ComparisonRow {
  kpiId: string;
  kpiName: string;
  kpiCode: string;
  category: string;
  unitOfMeasure: string;
  currentProposed: number;
  currentBudget: number;
  compareProposed: number;
  compareBudget: number;
  proposedVariance: number;
  budgetVariance: number;
  proposedVariancePercent: number;
  budgetVariancePercent: number;
}

export const BudgetComparison: React.FC<BudgetComparisonProps> = ({
  currentBudget,
  visible,
  onClose,
}) => {
  const [selectedBudgetId, setSelectedBudgetId] = React.useState<string>();
  const [comparisonData, setComparisonData] = React.useState<ComparisonRow[]>([]);

  // Fetch available budgets for comparison
  const { data: availableBudgets } = useQuery({
    queryKey: ['budgets-for-comparison', currentBudget.plantId],
    queryFn: () => budgetApi.getBudgets({
      plantId: currentBudget.plantId,
    }),
    enabled: visible,
  });

  // Fetch selected budget for comparison
  const { data: compareBudget } = useQuery({
    queryKey: ['budget', selectedBudgetId],
    queryFn: () => budgetApi.getBudget(selectedBudgetId!),
    enabled: !!selectedBudgetId,
  });

  // Fetch KPIs
  const { data: kpis } = useQuery({
    queryKey: ['kpis'],
    queryFn: budgetApi.getKPIs,
  });

  // Generate comparison data when both budgets and KPIs are available
  React.useEffect(() => {
    if (currentBudget && compareBudget && kpis) {
      const comparison = generateComparisonData(currentBudget, compareBudget, kpis);
      setComparisonData(comparison);
    }
  }, [currentBudget, compareBudget, kpis]);

  const generateComparisonData = (
    current: Budget,
    compare: Budget,
    kpiList: any[]
  ): ComparisonRow[] => {
    return kpiList.map(kpi => {
      const currentItems = current.items.filter(item => item.kpiId === kpi.id);
      const compareItems = compare.items.filter(item => item.kpiId === kpi.id);

      const currentProposed = currentItems.reduce((sum, item) => sum + (item.proposedValue || 0), 0);
      const currentBudget = currentItems.reduce((sum, item) => sum + (item.budgetValue || 0), 0);
      const compareProposed = compareItems.reduce((sum, item) => sum + (item.proposedValue || 0), 0);
      const compareBudget = compareItems.reduce((sum, item) => sum + (item.budgetValue || 0), 0);

      const proposedVariance = currentProposed - compareProposed;
      const budgetVariance = currentBudget - compareBudget;
      const proposedVariancePercent = compareProposed > 0 ? (proposedVariance / compareProposed) * 100 : 0;
      const budgetVariancePercent = compareBudget > 0 ? (budgetVariance / compareBudget) * 100 : 0;

      return {
        kpiId: kpi.id,
        kpiName: kpi.name,
        kpiCode: kpi.code,
        category: kpi.category.name,
        unitOfMeasure: kpi.unitOfMeasure,
        currentProposed,
        currentBudget,
        compareProposed,
        compareBudget,
        proposedVariance,
        budgetVariance,
        proposedVariancePercent,
        budgetVariancePercent,
      };
    });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('it-IT').format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getVarianceColor = (variance: number) => {
    if (Math.abs(variance) < 0.1) return '#1890ff'; // Blue for minimal variance
    if (variance > 0) return '#52c41a'; // Green for positive variance
    return '#ff4d4f'; // Red for negative variance
  };

  const getStatusText = (status: BudgetStatus) => {
    switch (status) {
      case BudgetStatus.Draft: return 'Bozza';
      case BudgetStatus.Submitted: return 'Inviato';
      case BudgetStatus.ApprovedPlant: return 'Approvato Impianto';
      case BudgetStatus.ApprovedHeadquarters: return 'Approvato Sede';
      case BudgetStatus.Reference: return 'Di Riferimento';
      case BudgetStatus.Rejected: return 'Rifiutato';
      default: return status;
    }
  };

  const getStatusColor = (status: BudgetStatus) => {
    switch (status) {
      case BudgetStatus.Draft: return 'default';
      case BudgetStatus.Submitted: return 'processing';
      case BudgetStatus.ApprovedPlant: return 'success';
      case BudgetStatus.ApprovedHeadquarters: return 'success';
      case BudgetStatus.Reference: return 'purple';
      case BudgetStatus.Rejected: return 'error';
      default: return 'default';
    }
  };

  const columns: ColumnsType<ComparisonRow> = [
    {
      title: 'KPI',
      dataIndex: 'kpiName',
      key: 'kpiName',
      width: 200,
      fixed: 'left',
    },
    {
      title: 'Categoria',
      dataIndex: 'category',
      key: 'category',
      width: 120,
    },
    {
      title: 'Unità',
      dataIndex: 'unitOfMeasure',
      key: 'unitOfMeasure',
      width: 80,
    },
    {
      title: `Budget Corrente (${currentBudget.year})`,
      children: [
        {
          title: 'Proposta',
          dataIndex: 'currentProposed',
          key: 'currentProposed',
          width: 120,
          align: 'right',
          render: (value: number) => formatNumber(value),
        },
        {
          title: 'Budget',
          dataIndex: 'currentBudget',
          key: 'currentBudget',
          width: 120,
          align: 'right',
          render: (value: number) => formatNumber(value),
        },
      ],
    },
    {
      title: `Budget Confronto (${compareBudget?.year || '-'})`,
      children: [
        {
          title: 'Proposta',
          dataIndex: 'compareProposed',
          key: 'compareProposed',
          width: 120,
          align: 'right',
          render: (value: number) => formatNumber(value),
        },
        {
          title: 'Budget',
          dataIndex: 'compareBudget',
          key: 'compareBudget',
          width: 120,
          align: 'right',
          render: (value: number) => formatNumber(value),
        },
      ],
    },
    {
      title: 'Varianza',
      children: [
        {
          title: 'Proposta',
          dataIndex: 'proposedVariance',
          key: 'proposedVariance',
          width: 120,
          align: 'right',
          render: (value: number, record: ComparisonRow) => (
            <Space direction="vertical" size="small">
              <Text style={{ color: getVarianceColor(value) }}>
                {formatNumber(value)}
              </Text>
              <Text style={{ color: getVarianceColor(record.proposedVariancePercent), fontSize: '12px' }}>
                {formatPercentage(record.proposedVariancePercent)}
              </Text>
            </Space>
          ),
        },
        {
          title: 'Budget',
          dataIndex: 'budgetVariance',
          key: 'budgetVariance',
          width: 120,
          align: 'right',
          render: (value: number, record: ComparisonRow) => (
            <Space direction="vertical" size="small">
              <Text style={{ color: getVarianceColor(value) }}>
                {formatNumber(value)}
              </Text>
              <Text style={{ color: getVarianceColor(record.budgetVariancePercent), fontSize: '12px' }}>
                {formatPercentage(record.budgetVariancePercent)}
              </Text>
            </Space>
          ),
        },
      ],
    },
  ];

  // Calculate summary statistics
  const summaryStats = React.useMemo(() => {
    if (comparisonData.length === 0) return null;

    const totalCurrentProposed = comparisonData.reduce((sum, row) => sum + row.currentProposed, 0);
    const totalCurrentBudget = comparisonData.reduce((sum, row) => sum + row.currentBudget, 0);
    const totalCompareProposed = comparisonData.reduce((sum, row) => sum + row.compareProposed, 0);
    const totalCompareBudget = comparisonData.reduce((sum, row) => sum + row.compareBudget, 0);

    const proposedVariance = totalCurrentProposed - totalCompareProposed;
    const budgetVariance = totalCurrentBudget - totalCompareBudget;
    const proposedVariancePercent = totalCompareProposed > 0 ? (proposedVariance / totalCompareProposed) * 100 : 0;
    const budgetVariancePercent = totalCompareBudget > 0 ? (budgetVariance / totalCompareBudget) * 100 : 0;

    return {
      totalCurrentProposed,
      totalCurrentBudget,
      totalCompareProposed,
      totalCompareBudget,
      proposedVariance,
      budgetVariance,
      proposedVariancePercent,
      budgetVariancePercent,
    };
  }, [comparisonData]);

  // Filter budgets for comparison (exclude current budget and only show approved/reference budgets)
  const eligibleBudgets = availableBudgets?.filter(budget => 
    budget.id !== currentBudget.id &&
    (budget.status === BudgetStatus.Reference || 
     budget.status === BudgetStatus.ApprovedHeadquarters ||
     budget.status === BudgetStatus.ApprovedPlant)
  ) || [];

  return (
    <Modal
      title="Confronto Budget"
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          Chiudi
        </Button>,
      ]}
      width={1200}
      style={{ top: 20 }}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <Card size="small">
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Title level={5}>Seleziona Budget per Confronto</Title>
            <Select
              style={{ width: '100%' }}
              placeholder="Seleziona un budget da confrontare"
              value={selectedBudgetId}
              onChange={setSelectedBudgetId}
              options={eligibleBudgets.map(budget => ({
                value: budget.id,
                label: (
                  <Space>
                    <Text>{budget.year}</Text>
                    <Tag color={getStatusColor(budget.status)}>
                      {getStatusText(budget.status)}
                    </Tag>
                    <Text type="secondary">v{budget.version}</Text>
                  </Space>
                ),
              }))}
            />
          </Space>
        </Card>

        {summaryStats && (
          <Card title="Riepilogo Confronto" size="small">
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="Varianza Proposta Totale"
                  value={summaryStats.proposedVariance}
                  formatter={(value) => formatNumber(Number(value))}
                  valueStyle={{ color: getVarianceColor(summaryStats.proposedVariance) }}
                  suffix={`(${formatPercentage(summaryStats.proposedVariancePercent)})`}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Varianza Budget Totale"
                  value={summaryStats.budgetVariance}
                  formatter={(value) => formatNumber(Number(value))}
                  valueStyle={{ color: getVarianceColor(summaryStats.budgetVariance) }}
                  suffix={`(${formatPercentage(summaryStats.budgetVariancePercent)})`}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Budget Corrente"
                  value={summaryStats.totalCurrentBudget}
                  formatter={(value) => formatNumber(Number(value))}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Budget Confronto"
                  value={summaryStats.totalCompareBudget}
                  formatter={(value) => formatNumber(Number(value))}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
            </Row>
          </Card>
        )}

        {comparisonData.length > 0 && (
          <Card title="Dettaglio Confronto per KPI" size="small">
            <Table
              columns={columns}
              dataSource={comparisonData}
              rowKey="kpiId"
              pagination={false}
              scroll={{ x: 1000, y: 400 }}
              size="small"
              bordered
            />
          </Card>
        )}

        {!selectedBudgetId && (
          <Card>
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <CompareArrowsOutlined style={{ fontSize: '48px', color: '#ccc' }} />
              <div style={{ marginTop: '16px', color: '#666' }}>
                Seleziona un budget per iniziare il confronto
              </div>
            </div>
          </Card>
        )}
      </Space>
    </Modal>
  );
};
