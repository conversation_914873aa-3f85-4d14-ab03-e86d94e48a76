import React, { useState } from 'react';
import { Card, Button, Space, Typography, Alert, Spin } from 'antd';
import { realBudgetApi } from '../services/realBudgetApi';

const { Title, Text, Paragraph } = Typography;

export const ApiTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testApi = async (testName: string, apiCall: () => Promise<any>) => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await apiCall();
      setResult({ testName, success: true, data: response });
    } catch (err) {
      setError(`${testName} failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setResult({ testName, success: false, error: err });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>🧪 API Test Dashboard</Title>
      <Paragraph>
        Test della connessione tra frontend e backend API.
      </Paragraph>

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="Test di Base">
          <Space wrap>
            <Button 
              type="primary"
              loading={loading}
              onClick={() => testApi('Test Endpoint', realBudgetApi.test)}
            >
              Test Basic Endpoint
            </Button>
            
            <Button 
              loading={loading}
              onClick={() => testApi('Health Check', realBudgetApi.health)}
            >
              Health Check
            </Button>
          </Space>
        </Card>

        <Card title="Test API Budget">
          <Space wrap>
            <Button 
              loading={loading}
              onClick={() => testApi('Get Budgets', () => realBudgetApi.getBudgets())}
            >
              Get Budgets
            </Button>
            
            <Button 
              loading={loading}
              onClick={() => testApi('Get Plants', () => realBudgetApi.getPlants())}
            >
              Get Plants
            </Button>
            
            <Button 
              loading={loading}
              onClick={() => testApi('Get KPIs', () => realBudgetApi.getKPIs())}
            >
              Get KPIs
            </Button>
          </Space>
        </Card>

        {loading && (
          <Card>
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin size="large" />
              <div style={{ marginTop: '16px' }}>
                <Text>Esecuzione test API...</Text>
              </div>
            </div>
          </Card>
        )}

        {error && (
          <Alert
            message="Errore API"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => setError(null)}
          />
        )}

        {result && !error && (
          <Card 
            title={`✅ Risultato: ${result.testName}`}
            extra={
              <Text type={result.success ? 'success' : 'danger'}>
                {result.success ? 'SUCCESS' : 'FAILED'}
              </Text>
            }
          >
            <pre style={{ 
              background: '#f5f5f5', 
              padding: '16px', 
              borderRadius: '4px',
              overflow: 'auto',
              maxHeight: '300px'
            }}>
              {JSON.stringify(result.data || result.error, null, 2)}
            </pre>
          </Card>
        )}

        <Card title="📊 Informazioni Sistema">
          <Space direction="vertical">
            <Text><strong>Frontend URL:</strong> http://localhost:3002</Text>
            <Text><strong>Backend URL:</strong> http://localhost:3001</Text>
            <Text><strong>Database:</strong> PostgreSQL 17 (Podman)</Text>
            <Text><strong>Status:</strong> {loading ? '🔄 Testing...' : '⏸️ Ready'}</Text>
          </Space>
        </Card>
      </Space>
    </div>
  );
};
