import React from 'react';
import { Modal, Table, Button, Space, Tag, Typography, Card, Descriptions, Alert, message } from 'antd';
import { LinkOutlined, EyeOutlined, CheckOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { budgetApi } from '../services/budgetApi';
import { MaintenancePlan, MaintenancePlanStatus, MaintenancePlanType, InterventionPriority } from '../types/budget';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;

interface MaintenancePlanAssociationProps {
  budgetId: string;
  plantId: string;
  year: number;
  currentPlanId?: string;
  onAssociate?: (planId: string) => void;
  onClose?: () => void;
  visible: boolean;
}

export const MaintenancePlanAssociation: React.FC<MaintenancePlanAssociationProps> = ({
  budgetId,
  plantId,
  year,
  currentPlanId,
  onAssociate,
  onClose,
  visible,
}) => {
  const queryClient = useQueryClient();
  const [selectedPlan, setSelectedPlan] = React.useState<MaintenancePlan | null>(null);
  const [detailVisible, setDetailVisible] = React.useState(false);

  const { data: availablePlans, isLoading } = useQuery({
    queryKey: ['maintenance-plans', plantId, year],
    queryFn: () => budgetApi.getMaintenancePlans(plantId, year),
    enabled: visible,
  });

  const associateMutation = useMutation({
    mutationFn: (planId: string) => budgetApi.associateMaintenancePlan(budgetId, planId),
    onSuccess: (_, planId) => {
      message.success('Piano di manutenzione associato con successo');
      queryClient.invalidateQueries({ queryKey: ['budget', budgetId] });
      onAssociate?.(planId);
      onClose?.();
    },
    onError: () => {
      message.error('Errore durante l\'associazione del piano di manutenzione');
    },
  });

  const handleAssociate = (plan: MaintenancePlan) => {
    Modal.confirm({
      title: 'Conferma associazione',
      content: `Sei sicuro di voler associare il piano di manutenzione "${plan.name}" a questo budget?`,
      onOk: () => associateMutation.mutate(plan.id),
    });
  };

  const handleViewDetails = (plan: MaintenancePlan) => {
    setSelectedPlan(plan);
    setDetailVisible(true);
  };

  const getStatusColor = (status: MaintenancePlanStatus) => {
    switch (status) {
      case MaintenancePlanStatus.Draft: return 'default';
      case MaintenancePlanStatus.Submitted: return 'processing';
      case MaintenancePlanStatus.Approved: return 'success';
      case MaintenancePlanStatus.Reference: return 'purple';
      case MaintenancePlanStatus.ProBudget: return 'gold';
      default: return 'default';
    }
  };

  const getStatusText = (status: MaintenancePlanStatus) => {
    switch (status) {
      case MaintenancePlanStatus.Draft: return 'Bozza';
      case MaintenancePlanStatus.Submitted: return 'Inviato';
      case MaintenancePlanStatus.Approved: return 'Approvato';
      case MaintenancePlanStatus.Reference: return 'Di Riferimento';
      case MaintenancePlanStatus.ProBudget: return 'Pro-Budget';
      default: return status;
    }
  };

  const getTypeText = (type: MaintenancePlanType) => {
    switch (type) {
      case MaintenancePlanType.Programmed: return 'Programmata';
      case MaintenancePlanType.Extraordinary: return 'Straordinaria';
      case MaintenancePlanType.Emergency: return 'Emergenza';
      default: return type;
    }
  };

  const getPriorityColor = (priority: InterventionPriority) => {
    switch (priority) {
      case InterventionPriority.Low: return 'green';
      case InterventionPriority.Medium: return 'orange';
      case InterventionPriority.High: return 'red';
      case InterventionPriority.Critical: return 'purple';
      default: return 'default';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const columns: ColumnsType<MaintenancePlan> = [
    {
      title: 'Codice',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: 'Nome',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Tipo',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: MaintenancePlanType) => getTypeText(type),
    },
    {
      title: 'Stato',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: MaintenancePlanStatus) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: 'Costo Totale',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 120,
      align: 'right',
      render: (cost: number) => formatCurrency(cost),
    },
    {
      title: 'Interventi',
      key: 'interventions',
      width: 100,
      align: 'center',
      render: (_, record) => record.interventions.length,
    },
    {
      title: 'Azioni',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetails(record)}
            title="Visualizza dettagli"
          />
          <Button
            type="text"
            icon={<LinkOutlined />}
            onClick={() => handleAssociate(record)}
            disabled={record.id === currentPlanId || associateMutation.isPending}
            title="Associa"
          />
        </Space>
      ),
    },
  ];

  const interventionColumns: ColumnsType<any> = [
    {
      title: 'Codice',
      dataIndex: 'code',
      key: 'code',
      width: 100,
    },
    {
      title: 'Descrizione',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Mese',
      dataIndex: 'plannedMonth',
      key: 'plannedMonth',
      width: 80,
      render: (month: number) => month + 1,
    },
    {
      title: 'Costo',
      dataIndex: 'estimatedCost',
      key: 'estimatedCost',
      width: 120,
      align: 'right',
      render: (cost: number) => formatCurrency(cost),
    },
    {
      title: 'Priorità',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: InterventionPriority) => (
        <Tag color={getPriorityColor(priority)}>
          {priority}
        </Tag>
      ),
    },
  ];

  // Filter plans to show only Reference and ProBudget status
  const eligiblePlans = availablePlans?.filter(plan => 
    plan.status === MaintenancePlanStatus.Reference || 
    plan.status === MaintenancePlanStatus.ProBudget
  ) || [];

  return (
    <>
      <Modal
        title="Associa Piano di Manutenzione"
        open={visible}
        onCancel={onClose}
        footer={null}
        width={1000}
      >
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {currentPlanId && (
            <Alert
              message="Piano di manutenzione già associato"
              description="Questo budget ha già un piano di manutenzione associato. Selezionando un nuovo piano, l'associazione precedente verrà sostituita."
              type="info"
              showIcon
            />
          )}

          <Card size="small">
            <Text type="secondary">
              Vengono mostrati solo i piani di manutenzione in stato "Di Riferimento" o "Pro-Budget" 
              per l'impianto e l'anno selezionati.
            </Text>
          </Card>

          <Table
            columns={columns}
            dataSource={eligiblePlans}
            rowKey="id"
            loading={isLoading}
            pagination={false}
            size="small"
            scroll={{ y: 400 }}
            rowSelection={{
              type: 'radio',
              selectedRowKeys: currentPlanId ? [currentPlanId] : [],
              onSelect: (record) => handleAssociate(record),
            }}
          />
        </Space>
      </Modal>

      {/* Detail Modal */}
      <Modal
        title={`Dettagli Piano: ${selectedPlan?.name}`}
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            Chiudi
          </Button>,
          selectedPlan && (
            <Button
              key="associate"
              type="primary"
              icon={<CheckOutlined />}
              onClick={() => {
                handleAssociate(selectedPlan);
                setDetailVisible(false);
              }}
              disabled={selectedPlan.id === currentPlanId}
            >
              Associa
            </Button>
          ),
        ]}
        width={800}
      >
        {selectedPlan && (
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <Descriptions column={2} bordered size="small">
              <Descriptions.Item label="Codice">{selectedPlan.code}</Descriptions.Item>
              <Descriptions.Item label="Anno">{selectedPlan.year}</Descriptions.Item>
              <Descriptions.Item label="Tipo">{getTypeText(selectedPlan.type)}</Descriptions.Item>
              <Descriptions.Item label="Stato">
                <Tag color={getStatusColor(selectedPlan.status)}>
                  {getStatusText(selectedPlan.status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Costo Totale" span={2}>
                {formatCurrency(selectedPlan.totalCost)}
              </Descriptions.Item>
              <Descriptions.Item label="Data Creazione">
                {new Date(selectedPlan.createdAt).toLocaleDateString('it-IT')}
              </Descriptions.Item>
              {selectedPlan.approvedAt && (
                <Descriptions.Item label="Data Approvazione">
                  {new Date(selectedPlan.approvedAt).toLocaleDateString('it-IT')}
                </Descriptions.Item>
              )}
            </Descriptions>

            <Card title="Interventi di Manutenzione" size="small">
              <Table
                columns={interventionColumns}
                dataSource={selectedPlan.interventions}
                rowKey="id"
                pagination={false}
                size="small"
                scroll={{ y: 300 }}
                summary={(pageData) => {
                  const total = pageData.reduce((sum, item) => sum + item.estimatedCost, 0);
                  return (
                    <Table.Summary.Row>
                      <Table.Summary.Cell index={0} colSpan={3}>
                        <strong>Totale</strong>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={3} align="right">
                        <strong>{formatCurrency(total)}</strong>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={4} />
                    </Table.Summary.Row>
                  );
                }}
              />
            </Card>
          </Space>
        )}
      </Modal>
    </>
  );
};
