import React from 'react';
import { Table, InputNumber, Checkbox, Button, Space, Card, Typography } from 'antd';
import { BudgetGridRow, MonthlyBudgetValue } from '../types/budget';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;

interface BudgetGridProps {
  budgetId?: string;
  year: number;
  data: BudgetGridRow[];
  readOnly?: boolean;
  onDataChange?: (data: BudgetGridRow[]) => void;
  onSave?: () => void;
  onCopyFromProposal?: () => void;
  onCalculateAutomatic?: () => void;
  onCalculateDerived?: () => void;
}

const MONTHS = [
  'GEN', 'FEB', 'MAR', 'APR', 'MAG', 'GIU',
  'LUG', 'AGO', 'SET', 'OTT', 'NOV', 'DIC'
];

export const BudgetGrid: React.FC<BudgetGridProps> = ({
  budgetId,
  year,
  data,
  readOnly = false,
  onDataChange,
  onSave,
  onCopyFromProposal,
  onCalculateAutomatic,
  onCalculateDerived,
}) => {
  const handleValueChange = (
    rowIndex: number,
    month: number,
    field: 'proposedValue' | 'budgetValue',
    value: number | null
  ) => {
    if (readOnly || !onDataChange) return;

    const newData = [...data];
    const monthlyValue = newData[rowIndex].monthlyValues.find(mv => mv.month === month);
    
    if (monthlyValue) {
      monthlyValue[field] = value || undefined;
    } else {
      newData[rowIndex].monthlyValues.push({
        month,
        [field]: value || undefined,
      });
    }
    
    onDataChange(newData);
  };

  const handleAutoCalculatedChange = (rowIndex: number, checked: boolean) => {
    if (readOnly || !onDataChange) return;

    const newData = [...data];
    newData[rowIndex].isAutoCalculated = checked;
    onDataChange(newData);
  };

  const getMonthValue = (row: BudgetGridRow, month: number, field: 'proposedValue' | 'budgetValue') => {
    const monthlyValue = row.monthlyValues.find(mv => mv.month === month);
    return monthlyValue?.[field];
  };

  const formatNumber = (value: number | undefined) => {
    if (value === undefined || value === null) return '';
    return value.toLocaleString('it-IT', { 
      minimumFractionDigits: 0,
      maximumFractionDigits: 2 
    });
  };

  // Fixed columns (KPI info)
  const fixedColumns: ColumnsType<BudgetGridRow> = [
    {
      title: 'Calc.',
      dataIndex: 'isAutoCalculated',
      key: 'calc',
      width: 50,
      fixed: 'left',
      render: (value: boolean, record: BudgetGridRow, index: number) => (
        <Checkbox
          checked={value}
          onChange={(e) => handleAutoCalculatedChange(index, e.target.checked)}
          disabled={readOnly}
        />
      ),
    },
    {
      title: 'KPI',
      dataIndex: ['kpi', 'name'],
      key: 'kpi',
      width: 200,
      fixed: 'left',
      render: (text: string, record: BudgetGridRow) => (
        <div
          style={{
            backgroundColor: record.kpi.category.color,
            padding: '4px 8px',
            borderRadius: '4px',
            color: '#000',
            fontSize: '12px',
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: 'Unità',
      dataIndex: ['kpi', 'unitOfMeasure'],
      key: 'unit',
      width: 80,
      fixed: 'left',
      render: (text: string, record: BudgetGridRow) => (
        <div
          style={{
            backgroundColor: record.kpi.category.color,
            padding: '4px 8px',
            borderRadius: '4px',
            color: '#000',
            fontSize: '12px',
            textAlign: 'center',
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: 'Proposta',
      key: 'proposed-annual',
      width: 100,
      fixed: 'left',
      render: (_, record: BudgetGridRow) => {
        const total = record.monthlyValues.reduce((sum, mv) => sum + (mv.proposedValue || 0), 0);
        return (
          <div
            style={{
              backgroundColor: record.kpi.category.color,
              padding: '4px 8px',
              borderRadius: '4px',
              color: '#000',
              fontSize: '12px',
              textAlign: 'right',
            }}
          >
            {formatNumber(total)}
          </div>
        );
      },
    },
    {
      title: 'Obiettivo',
      key: 'budget-annual',
      width: 100,
      fixed: 'left',
      render: (_, record: BudgetGridRow) => {
        const total = record.monthlyValues.reduce((sum, mv) => sum + (mv.budgetValue || 0), 0);
        return (
          <div
            style={{
              backgroundColor: record.kpi.category.color,
              padding: '4px 8px',
              borderRadius: '4px',
              color: '#000',
              fontSize: '12px',
              textAlign: 'right',
            }}
          >
            {formatNumber(total)}
          </div>
        );
      },
    },
  ];

  // Monthly columns
  const monthlyColumns: ColumnsType<BudgetGridRow> = MONTHS.flatMap((month, monthIndex) => [
    {
      title: month,
      children: [
        {
          title: 'Proposta',
          key: `proposed-${monthIndex}`,
          width: 80,
          render: (_, record: BudgetGridRow, rowIndex: number) => (
            <InputNumber
              size="small"
              value={getMonthValue(record, monthIndex, 'proposedValue')}
              onChange={(value) => handleValueChange(rowIndex, monthIndex, 'proposedValue', value)}
              disabled={readOnly || record.isAutoCalculated}
              style={{
                width: '100%',
                backgroundColor: record.kpi.category.color,
              }}
              formatter={(value) => value ? formatNumber(Number(value)) : ''}
              parser={(value) => value ? Number(value.replace(/\$\s?|(,*)/g, '')) : 0}
            />
          ),
        },
        {
          title: 'Obiettivo',
          key: `budget-${monthIndex}`,
          width: 80,
          render: (_, record: BudgetGridRow, rowIndex: number) => (
            <InputNumber
              size="small"
              value={getMonthValue(record, monthIndex, 'budgetValue')}
              onChange={(value) => handleValueChange(rowIndex, monthIndex, 'budgetValue', value)}
              disabled={readOnly}
              style={{
                width: '100%',
                backgroundColor: record.kpi.category.color,
              }}
              formatter={(value) => value ? formatNumber(Number(value)) : ''}
              parser={(value) => value ? Number(value.replace(/\$\s?|(,*)/g, '')) : 0}
            />
          ),
        },
      ],
    },
  ]);

  const allColumns = [...fixedColumns, ...monthlyColumns];

  return (
    <Card>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={4}>
          Gestione Piano di Budget Annuale - Anno {year}
        </Title>
        {!readOnly && (
          <Space>
            <Button onClick={onCopyFromProposal} disabled={!onCopyFromProposal}>
              Copia dati da proposta
            </Button>
            <Button onClick={onCalculateAutomatic} disabled={!onCalculateAutomatic}>
              Ricalcola automatici
            </Button>
            <Button onClick={onCalculateDerived} disabled={!onCalculateDerived}>
              Calcola derivati
            </Button>
            <Button type="primary" onClick={onSave} disabled={!onSave}>
              Salva
            </Button>
          </Space>
        )}
      </div>

      <Table
        columns={allColumns}
        dataSource={data}
        rowKey={(record) => record.kpi.id}
        pagination={false}
        scroll={{ x: 1500, y: 600 }}
        size="small"
        bordered
        sticky
      />
    </Card>
  );
};
