import React from 'react';
import { Table, InputNumber, Checkbox, Button, Space, Card, Typography, message, Modal, Dropdown, Progress } from 'antd';
import { DownOutlined, SaveOutlined, DownloadOutlined, HistoryOutlined } from '@ant-design/icons';
import { BudgetGridRow, MonthlyBudgetValue } from '../types/budget';
import { CalculationService } from '../services/calculationService';
import { SaveService } from '../services/saveService';
import type { ColumnsType } from 'antd/es/table';
import type { MenuProps } from 'antd';

const { Title } = Typography;

interface BudgetGridProps {
  budgetId?: string;
  year: number;
  data: BudgetGridRow[];
  readOnly?: boolean;
  onDataChange?: (data: BudgetGridRow[]) => void;
  onSave?: () => void;
  onCopyFromProposal?: () => void;
  onCalculateAutomatic?: () => void;
  onCalculateDerived?: () => void;
}

const MONTHS = [
  'GEN', 'FEB', 'MAR', 'APR', 'MAG', 'GIU',
  'LUG', 'AGO', 'SET', 'OTT', 'NOV', 'DIC'
];

export const BudgetGrid: React.FC<BudgetGridProps> = ({
  budgetId,
  year,
  data,
  readOnly = false,
  onDataChange,
  onSave,
  onCopyFromProposal,
  onCalculateAutomatic,
  onCalculateDerived,
}) => {
  const [isCalculating, setIsCalculating] = React.useState(false);
  const [isSaving, setIsSaving] = React.useState(false);
  const [saveProgress, setSaveProgress] = React.useState(0);
  const [autoSaveEnabled, setAutoSaveEnabled] = React.useState(false);
  const autoSaveRef = React.useRef<{ start: () => void; stop: () => void; saveNow: () => Promise<any> } | null>(null);

  const handleCopyFromProposal = async () => {
    if (!onDataChange) return;

    Modal.confirm({
      title: 'Conferma operazione',
      content: 'Sei sicuro di voler copiare tutti i valori dalla colonna "Proposta" alla colonna "Obiettivo"? Questa operazione sovrascriverà i valori esistenti.',
      onOk: () => {
        setIsCalculating(true);
        try {
          const newData = CalculationService.copyFromProposal(data);
          onDataChange(newData);
          message.success('Dati copiati dalla proposta con successo');
          onCopyFromProposal?.();
        } catch (error) {
          message.error('Errore durante la copia dei dati');
        } finally {
          setIsCalculating(false);
        }
      },
    });
  };

  const handleCalculateAutomatic = async () => {
    if (!onDataChange) return;

    Modal.confirm({
      title: 'Conferma ricalcolo',
      content: 'Sei sicuro di voler ricalcolare tutti i KPI automatici? I valori esistenti verranno sovrascritti.',
      onOk: () => {
        setIsCalculating(true);
        try {
          const newData = CalculationService.calculateAutomatic(data);
          onDataChange(newData);
          message.success('KPI automatici ricalcolati con successo');
          onCalculateAutomatic?.();
        } catch (error) {
          message.error('Errore durante il ricalcolo automatico');
        } finally {
          setIsCalculating(false);
        }
      },
    });
  };

  const handleCalculateDerived = async () => {
    if (!onDataChange) return;

    setIsCalculating(true);
    try {
      const newData = CalculationService.calculateDerived(data);
      onDataChange(newData);
      message.success('KPI derivati calcolati con successo');
      onCalculateDerived?.();
    } catch (error) {
      message.error('Errore durante il calcolo dei derivati');
    } finally {
      setIsCalculating(false);
    }
  };

  // Initialize auto-save
  React.useEffect(() => {
    if (budgetId && !readOnly) {
      autoSaveRef.current = SaveService.createAutoSave(
        budgetId,
        () => data,
        30 // Auto-save every 30 seconds
      );
    }

    return () => {
      if (autoSaveRef.current) {
        autoSaveRef.current.stop();
      }
    };
  }, [budgetId, readOnly]);

  // Toggle auto-save
  React.useEffect(() => {
    if (autoSaveRef.current) {
      if (autoSaveEnabled) {
        autoSaveRef.current.start();
      } else {
        autoSaveRef.current.stop();
      }
    }
  }, [autoSaveEnabled]);

  const handleSave = async () => {
    if (!budgetId) return;

    setIsSaving(true);
    setSaveProgress(0);

    try {
      const result = await SaveService.saveBudgetData(budgetId, data, {
        validateBeforeSave: true,
        applyBusinessRules: true,
        calculateDerived: true,
        showProgress: true,
      });

      if (result.success) {
        message.success(`Budget salvato con successo (${result.savedItems} elementi)`);
        if (result.warnings.length > 0) {
          message.info(result.warnings.join(', '));
        }
        onSave?.();
      } else {
        if (result.validationErrors.length > 0) {
          Modal.error({
            title: 'Errori di validazione',
            content: (
              <div>
                <p>Sono stati trovati i seguenti errori:</p>
                <ul>
                  {result.validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            ),
          });
        } else {
          Modal.error({
            title: 'Errori di salvataggio',
            content: (
              <div>
                <p>Si sono verificati errori durante il salvataggio:</p>
                <ul>
                  {result.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            ),
          });
        }
      }
    } catch (error) {
      message.error('Errore durante il salvataggio');
    } finally {
      setIsSaving(false);
      setSaveProgress(0);
    }
  };

  const handleExportCSV = () => {
    SaveService.exportToCSV(data, `budget-${year}-${budgetId}.csv`);
    message.success('Dati esportati in CSV');
  };

  const handleCreateBackup = () => {
    if (!budgetId) return;

    try {
      const backupKey = SaveService.createBackup(data, budgetId);
      message.success('Backup creato con successo');
    } catch (error) {
      message.error('Errore durante la creazione del backup');
    }
  };

  const handleRestoreBackup = () => {
    if (!budgetId || !onDataChange) return;

    const backups = SaveService.listBackups(budgetId);
    if (backups.length === 0) {
      message.info('Nessun backup disponibile');
      return;
    }

    Modal.confirm({
      title: 'Ripristina Backup',
      content: `Vuoi ripristinare l'ultimo backup del ${new Date(backups[0].timestamp).toLocaleString('it-IT')}?`,
      onOk: () => {
        const restoredData = SaveService.restoreFromBackup(backups[0].key);
        if (restoredData) {
          onDataChange(restoredData);
          message.success('Backup ripristinato con successo');
        } else {
          message.error('Errore durante il ripristino del backup');
        }
      },
    });
  };
  const handleValueChange = (
    rowIndex: number,
    month: number,
    field: 'proposedValue' | 'budgetValue',
    value: number | null
  ) => {
    if (readOnly || !onDataChange) return;

    const newData = [...data];
    const monthlyValue = newData[rowIndex].monthlyValues.find(mv => mv.month === month);
    
    if (monthlyValue) {
      monthlyValue[field] = value || undefined;
    } else {
      newData[rowIndex].monthlyValues.push({
        month,
        [field]: value || undefined,
      });
    }
    
    onDataChange(newData);
  };

  const handleAutoCalculatedChange = (rowIndex: number, checked: boolean) => {
    if (readOnly || !onDataChange) return;

    const newData = [...data];
    newData[rowIndex].isAutoCalculated = checked;
    onDataChange(newData);
  };

  const getMonthValue = (row: BudgetGridRow, month: number, field: 'proposedValue' | 'budgetValue') => {
    const monthlyValue = row.monthlyValues.find(mv => mv.month === month);
    return monthlyValue?.[field];
  };

  const formatNumber = (value: number | undefined) => {
    if (value === undefined || value === null) return '';
    return value.toLocaleString('it-IT', { 
      minimumFractionDigits: 0,
      maximumFractionDigits: 2 
    });
  };

  // Fixed columns (KPI info)
  const fixedColumns: ColumnsType<BudgetGridRow> = [
    {
      title: 'Calc.',
      dataIndex: 'isAutoCalculated',
      key: 'calc',
      width: 50,
      fixed: 'left',
      render: (value: boolean, record: BudgetGridRow, index: number) => (
        <Checkbox
          checked={value}
          onChange={(e) => handleAutoCalculatedChange(index, e.target.checked)}
          disabled={readOnly}
        />
      ),
    },
    {
      title: 'KPI',
      dataIndex: ['kpi', 'name'],
      key: 'kpi',
      width: 200,
      fixed: 'left',
      render: (text: string, record: BudgetGridRow) => (
        <div
          style={{
            backgroundColor: record.kpi.category.color,
            padding: '4px 8px',
            borderRadius: '4px',
            color: '#000',
            fontSize: '12px',
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: 'Unità',
      dataIndex: ['kpi', 'unitOfMeasure'],
      key: 'unit',
      width: 80,
      fixed: 'left',
      render: (text: string, record: BudgetGridRow) => (
        <div
          style={{
            backgroundColor: record.kpi.category.color,
            padding: '4px 8px',
            borderRadius: '4px',
            color: '#000',
            fontSize: '12px',
            textAlign: 'center',
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: 'Proposta',
      key: 'proposed-annual',
      width: 100,
      fixed: 'left',
      render: (_, record: BudgetGridRow) => {
        const total = record.monthlyValues.reduce((sum, mv) => sum + (mv.proposedValue || 0), 0);
        return (
          <div
            style={{
              backgroundColor: record.kpi.category.color,
              padding: '4px 8px',
              borderRadius: '4px',
              color: '#000',
              fontSize: '12px',
              textAlign: 'right',
            }}
          >
            {formatNumber(total)}
          </div>
        );
      },
    },
    {
      title: 'Obiettivo',
      key: 'budget-annual',
      width: 100,
      fixed: 'left',
      render: (_, record: BudgetGridRow) => {
        const total = record.monthlyValues.reduce((sum, mv) => sum + (mv.budgetValue || 0), 0);
        return (
          <div
            style={{
              backgroundColor: record.kpi.category.color,
              padding: '4px 8px',
              borderRadius: '4px',
              color: '#000',
              fontSize: '12px',
              textAlign: 'right',
            }}
          >
            {formatNumber(total)}
          </div>
        );
      },
    },
  ];

  // Monthly columns
  const monthlyColumns: ColumnsType<BudgetGridRow> = MONTHS.flatMap((month, monthIndex) => [
    {
      title: month,
      children: [
        {
          title: 'Proposta',
          key: `proposed-${monthIndex}`,
          width: 80,
          render: (_, record: BudgetGridRow, rowIndex: number) => (
            <InputNumber
              size="small"
              value={getMonthValue(record, monthIndex, 'proposedValue')}
              onChange={(value) => handleValueChange(rowIndex, monthIndex, 'proposedValue', value)}
              disabled={readOnly || record.isAutoCalculated}
              style={{
                width: '100%',
                backgroundColor: record.kpi.category.color,
              }}
              formatter={(value) => value ? formatNumber(Number(value)) : ''}
              parser={(value) => value ? Number(value.replace(/\$\s?|(,*)/g, '')) : 0}
            />
          ),
        },
        {
          title: 'Obiettivo',
          key: `budget-${monthIndex}`,
          width: 80,
          render: (_, record: BudgetGridRow, rowIndex: number) => (
            <InputNumber
              size="small"
              value={getMonthValue(record, monthIndex, 'budgetValue')}
              onChange={(value) => handleValueChange(rowIndex, monthIndex, 'budgetValue', value)}
              disabled={readOnly}
              style={{
                width: '100%',
                backgroundColor: record.kpi.category.color,
              }}
              formatter={(value) => value ? formatNumber(Number(value)) : ''}
              parser={(value) => value ? Number(value.replace(/\$\s?|(,*)/g, '')) : 0}
            />
          ),
        },
      ],
    },
  ]);

  const allColumns = [...fixedColumns, ...monthlyColumns];

  return (
    <Card>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={4}>
          Gestione Piano di Budget Annuale - Anno {year}
        </Title>
        {!readOnly && (
          <Space>
            <Button
              onClick={handleCopyFromProposal}
              disabled={isCalculating || isSaving}
              loading={isCalculating}
            >
              Copia dati da proposta
            </Button>
            <Button
              onClick={handleCalculateAutomatic}
              disabled={isCalculating || isSaving}
              loading={isCalculating}
            >
              Ricalcola automatici
            </Button>
            <Button
              onClick={handleCalculateDerived}
              disabled={isCalculating || isSaving}
              loading={isCalculating}
            >
              Calcola derivati
            </Button>

            <Dropdown
              menu={{
                items: [
                  {
                    key: 'save',
                    label: 'Salva',
                    icon: <SaveOutlined />,
                    onClick: handleSave,
                  },
                  {
                    key: 'auto-save',
                    label: autoSaveEnabled ? 'Disabilita Auto-Save' : 'Abilita Auto-Save',
                    onClick: () => setAutoSaveEnabled(!autoSaveEnabled),
                  },
                  { type: 'divider' },
                  {
                    key: 'export',
                    label: 'Esporta CSV',
                    icon: <DownloadOutlined />,
                    onClick: handleExportCSV,
                  },
                  {
                    key: 'backup',
                    label: 'Crea Backup',
                    onClick: handleCreateBackup,
                  },
                  {
                    key: 'restore',
                    label: 'Ripristina Backup',
                    icon: <HistoryOutlined />,
                    onClick: handleRestoreBackup,
                  },
                ] as MenuProps['items'],
              }}
              trigger={['click']}
            >
              <Button
                type="primary"
                disabled={isCalculating || isSaving}
                loading={isSaving}
              >
                Salva <DownOutlined />
              </Button>
            </Dropdown>
          </Space>
        )}

        {isSaving && saveProgress > 0 && (
          <Progress
            percent={saveProgress}
            size="small"
            style={{ marginTop: 8 }}
          />
        )}

        {autoSaveEnabled && !readOnly && (
          <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
            Auto-save attivo (ogni 30 secondi)
          </div>
        )}
      </div>

      <Table
        columns={allColumns}
        dataSource={data}
        rowKey={(record) => record.kpi.id}
        pagination={false}
        scroll={{ x: 1500, y: 600 }}
        size="small"
        bordered
        sticky
      />
    </Card>
  );
};
