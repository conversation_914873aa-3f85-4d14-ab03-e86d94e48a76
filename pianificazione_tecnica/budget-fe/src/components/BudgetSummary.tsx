import React from 'react';
import { Card, Statistic, Row, Col, Table, Typography, Progress } from 'antd';
import { BudgetGridRow } from '../types/budget';
import { CalculationService } from '../services/calculationService';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;

interface BudgetSummaryProps {
  data: BudgetGridRow[];
  year: number;
}

interface CategorySummary {
  category: string;
  proposed: number;
  budget: number;
  variance: number;
  variancePercent: number;
}

export const BudgetSummary: React.FC<BudgetSummaryProps> = ({ data, year }) => {
  const report = React.useMemo(() => {
    return CalculationService.generateCalculationReport(data);
  }, [data]);

  const categoryData: CategorySummary[] = React.useMemo(() => {
    return Object.entries(report.categoryTotals).map(([category, totals]) => ({
      category,
      proposed: totals.proposed,
      budget: totals.budget,
      variance: totals.budget - totals.proposed,
      variancePercent: totals.proposed > 0 ? ((totals.budget - totals.proposed) / totals.proposed) * 100 : 0,
    }));
  }, [report.categoryTotals]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('it-IT').format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getVarianceColor = (variance: number) => {
    if (variance > 0) return '#52c41a'; // Green for positive variance
    if (variance < 0) return '#ff4d4f'; // Red for negative variance
    return '#1890ff'; // Blue for no variance
  };

  const columns: ColumnsType<CategorySummary> = [
    {
      title: 'Categoria',
      dataIndex: 'category',
      key: 'category',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: 'Proposta',
      dataIndex: 'proposed',
      key: 'proposed',
      align: 'right',
      render: (value: number) => formatCurrency(value),
    },
    {
      title: 'Budget',
      dataIndex: 'budget',
      key: 'budget',
      align: 'right',
      render: (value: number) => formatCurrency(value),
    },
    {
      title: 'Varianza',
      dataIndex: 'variance',
      key: 'variance',
      align: 'right',
      render: (value: number) => (
        <span style={{ color: getVarianceColor(value) }}>
          {formatCurrency(value)}
        </span>
      ),
    },
    {
      title: 'Varianza %',
      dataIndex: 'variancePercent',
      key: 'variancePercent',
      align: 'right',
      render: (value: number) => (
        <span style={{ color: getVarianceColor(value) }}>
          {formatPercentage(value)}
        </span>
      ),
    },
  ];

  const totalVariance = report.totalBudget - report.totalProposed;
  const totalVariancePercent = report.totalProposed > 0 
    ? ((report.totalBudget - report.totalProposed) / report.totalProposed) * 100 
    : 0;

  const completionRate = data.length > 0 
    ? (data.filter(row => row.monthlyValues.some(mv => mv.budgetValue !== undefined)).length / data.length) * 100
    : 0;

  return (
    <div style={{ marginBottom: 24 }}>
      <Title level={4}>Riepilogo Budget {year}</Title>
      
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Totale Proposta"
              value={report.totalProposed}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Totale Budget"
              value={report.totalBudget}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Varianza Totale"
              value={totalVariance}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: getVarianceColor(totalVariance) }}
              suffix={`(${formatPercentage(totalVariancePercent)})`}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Completamento"
              value={completionRate}
              formatter={(value) => formatPercentage(Number(value))}
              valueStyle={{ color: completionRate >= 80 ? '#52c41a' : '#faad14' }}
            />
            <Progress 
              percent={completionRate} 
              showInfo={false} 
              strokeColor={completionRate >= 80 ? '#52c41a' : '#faad14'}
              size="small"
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Card title="KPI Overview" size="small">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="KPI Manuali"
                  value={report.manualKpis}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="KPI Automatici"
                  value={report.autoCalculatedKpis}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
        
        <Col xs={24} md={12}>
          <Card title="Distribuzione per Categoria" size="small">
            <Table
              columns={columns}
              dataSource={categoryData}
              rowKey="category"
              pagination={false}
              size="small"
              summary={(pageData) => {
                const totalProposed = pageData.reduce((sum, item) => sum + item.proposed, 0);
                const totalBudget = pageData.reduce((sum, item) => sum + item.budget, 0);
                const totalVar = totalBudget - totalProposed;
                const totalVarPercent = totalProposed > 0 ? (totalVar / totalProposed) * 100 : 0;

                return (
                  <Table.Summary.Row style={{ backgroundColor: '#fafafa' }}>
                    <Table.Summary.Cell index={0}>
                      <strong>Totale</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={1} align="right">
                      <strong>{formatCurrency(totalProposed)}</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={2} align="right">
                      <strong>{formatCurrency(totalBudget)}</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3} align="right">
                      <strong style={{ color: getVarianceColor(totalVar) }}>
                        {formatCurrency(totalVar)}
                      </strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4} align="right">
                      <strong style={{ color: getVarianceColor(totalVar) }}>
                        {formatPercentage(totalVarPercent)}
                      </strong>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                );
              }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};
