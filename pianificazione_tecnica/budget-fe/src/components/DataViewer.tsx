import React from 'react';
import { Card, Tabs, Table, Typography, Space, Tag, Descriptions } from 'antd';
import { useQuery } from '@tanstack/react-query';
import { budgetApi } from '../services/budgetApi';
import { BudgetStatus, PlantType } from '../types/budget';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;

export const DataViewer: React.FC = () => {
  const { data: budgets } = useQuery({
    queryKey: ['all-budgets'],
    queryFn: () => budgetApi.getBudgets(),
  });

  const { data: plants } = useQuery({
    queryKey: ['all-plants'],
    queryFn: () => budgetApi.getPlants(),
  });

  const { data: kpis } = useQuery({
    queryKey: ['all-kpis'],
    queryFn: budgetApi.getKPIs,
  });

  const { data: categories } = useQuery({
    queryKey: ['all-categories'],
    queryFn: budgetApi.getKPICategories,
  });

  const { data: maintenancePlans } = useQuery({
    queryKey: ['all-maintenance-plans'],
    queryFn: () => budgetApi.getMaintenancePlans('plant-1', 2025),
  });

  const getStatusColor = (status: BudgetStatus) => {
    switch (status) {
      case BudgetStatus.Draft: return 'default';
      case BudgetStatus.Submitted: return 'processing';
      case BudgetStatus.ApprovedPlant: return 'success';
      case BudgetStatus.ApprovedHeadquarters: return 'success';
      case BudgetStatus.Reference: return 'purple';
      case BudgetStatus.Rejected: return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: BudgetStatus) => {
    switch (status) {
      case BudgetStatus.Draft: return 'Bozza';
      case BudgetStatus.Submitted: return 'Inviato';
      case BudgetStatus.ApprovedPlant: return 'Approvato Impianto';
      case BudgetStatus.ApprovedHeadquarters: return 'Approvato Sede';
      case BudgetStatus.Reference: return 'Di Riferimento';
      case BudgetStatus.Rejected: return 'Rifiutato';
      default: return status;
    }
  };

  const getPlantTypeText = (type: PlantType) => {
    switch (type) {
      case PlantType.Thermal: return 'Termoelettrico';
      case PlantType.Hydroelectric: return 'Idroelettrico';
      default: return type;
    }
  };

  const budgetColumns: ColumnsType<any> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
    },
    {
      title: 'Anno',
      dataIndex: 'year',
      key: 'year',
      width: 80,
    },
    {
      title: 'Impianto',
      dataIndex: 'plantId',
      key: 'plantId',
      width: 120,
    },
    {
      title: 'Unità Produttiva',
      dataIndex: 'productionUnitId',
      key: 'productionUnitId',
      width: 150,
    },
    {
      title: 'Stato',
      dataIndex: 'status',
      key: 'status',
      width: 150,
      render: (status: BudgetStatus) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: 'Versione',
      dataIndex: 'version',
      key: 'version',
      width: 80,
    },
    {
      title: 'Creato Da',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 120,
    },
    {
      title: 'Data Creazione',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => new Date(date).toLocaleDateString('it-IT'),
    },
    {
      title: 'Piano Manutenzione',
      dataIndex: 'maintenancePlanId',
      key: 'maintenancePlanId',
      width: 150,
    },
    {
      title: 'N. Items',
      key: 'itemsCount',
      width: 80,
      render: (_, record) => record.items?.length || 0,
    },
  ];

  const plantColumns: ColumnsType<any> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
    },
    {
      title: 'Nome',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Codice',
      dataIndex: 'code',
      key: 'code',
      width: 100,
    },
    {
      title: 'Tipo',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: PlantType) => (
        <Tag color={type === PlantType.Thermal ? 'red' : 'blue'}>
          {getPlantTypeText(type)}
        </Tag>
      ),
    },
    {
      title: 'Località',
      dataIndex: 'location',
      key: 'location',
      width: 150,
    },
    {
      title: 'Attivo',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Sì' : 'No'}
        </Tag>
      ),
    },
  ];

  const kpiColumns: ColumnsType<any> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
    },
    {
      title: 'Codice',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: 'Nome',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Unità di Misura',
      dataIndex: 'unitOfMeasure',
      key: 'unitOfMeasure',
      width: 120,
    },
    {
      title: 'Categoria',
      key: 'category',
      width: 150,
      render: (_, record) => (
        <Tag color={record.category?.color}>
          {record.category?.name}
        </Tag>
      ),
    },
    {
      title: 'Auto Calcolato',
      dataIndex: 'isAutoCalculated',
      key: 'isAutoCalculated',
      width: 120,
      render: (value: boolean) => (
        <Tag color={value ? 'green' : 'default'}>
          {value ? 'Sì' : 'No'}
        </Tag>
      ),
    },
    {
      title: 'Formula',
      dataIndex: 'calculationFormula',
      key: 'calculationFormula',
      width: 200,
      render: (formula: string) => formula || '-',
    },
    {
      title: 'Ordine',
      dataIndex: 'displayOrder',
      key: 'displayOrder',
      width: 80,
    },
  ];

  const categoryColumns: ColumnsType<any> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
    },
    {
      title: 'Nome',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Colore',
      key: 'color',
      width: 100,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <div
            style={{
              width: 20,
              height: 20,
              backgroundColor: record.color,
              border: '1px solid #ccc',
              borderRadius: 4,
            }}
          />
          <Text>{record.color}</Text>
        </div>
      ),
    },
    {
      title: 'Ordine',
      dataIndex: 'displayOrder',
      key: 'displayOrder',
      width: 80,
    },
  ];

  const maintenanceColumns: ColumnsType<any> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
    },
    {
      title: 'Codice',
      dataIndex: 'code',
      key: 'code',
      width: 150,
    },
    {
      title: 'Nome',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Anno',
      dataIndex: 'year',
      key: 'year',
      width: 80,
    },
    {
      title: 'Stato',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color="blue">{status}</Tag>
      ),
    },
    {
      title: 'Tipo',
      dataIndex: 'type',
      key: 'type',
      width: 120,
    },
    {
      title: 'Costo Totale',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 120,
      align: 'right',
      render: (cost: number) => new Intl.NumberFormat('it-IT', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 0,
      }).format(cost),
    },
    {
      title: 'N. Interventi',
      key: 'interventionsCount',
      width: 100,
      render: (_, record) => record.interventions?.length || 0,
    },
  ];

  const tabItems = [
    {
      key: 'budgets',
      label: `Budget (${budgets?.length || 0})`,
      children: (
        <Table
          columns={budgetColumns}
          dataSource={budgets}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          scroll={{ x: 1200 }}
          size="small"
        />
      ),
    },
    {
      key: 'plants',
      label: `Impianti (${plants?.length || 0})`,
      children: (
        <Table
          columns={plantColumns}
          dataSource={plants}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          size="small"
        />
      ),
    },
    {
      key: 'kpis',
      label: `KPI (${kpis?.length || 0})`,
      children: (
        <Table
          columns={kpiColumns}
          dataSource={kpis}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          scroll={{ x: 1000 }}
          size="small"
        />
      ),
    },
    {
      key: 'categories',
      label: `Categorie (${categories?.length || 0})`,
      children: (
        <Table
          columns={categoryColumns}
          dataSource={categories}
          rowKey="id"
          pagination={false}
          size="small"
        />
      ),
    },
    {
      key: 'maintenance',
      label: `Piani Manutenzione (${maintenancePlans?.length || 0})`,
      children: (
        <Table
          columns={maintenanceColumns}
          dataSource={maintenancePlans}
          rowKey="id"
          pagination={false}
          scroll={{ x: 1000 }}
          size="small"
        />
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card>
          <Title level={3}>Visualizzatore Dati Mock</Title>
          <Text type="secondary">
            Questa pagina mostra tutti i dati mock utilizzati dall'applicazione per simulare 
            il comportamento del database. In un ambiente di produzione, questi dati 
            verrebbero sostituiti con chiamate API reali a un database PostgreSQL.
          </Text>
        </Card>

        <Card>
          <Tabs items={tabItems} />
        </Card>
      </Space>
    </div>
  );
};
