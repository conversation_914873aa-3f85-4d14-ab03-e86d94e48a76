import React from 'react';
import { Form, Select, But<PERSON>, Card, message, Space } from 'antd';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { budgetApi } from '../services/budgetApi';
import { CreateBudgetRequest, PlantType, UserType } from '../types/budget';

interface CreateBudgetFormProps {
  onSuccess?: (budgetId: string) => void;
  onCancel?: () => void;
}

interface FormValues {
  plantId: string;
  productionUnitId: string;
  year: number;
  exerciseType?: PlantType;
}

export const CreateBudgetForm: React.FC<CreateBudgetFormProps> = ({ onSuccess, onCancel }) => {
  const [form] = Form.useForm<FormValues>();
  const queryClient = useQueryClient();
  const [selectedPlantId, setSelectedPlantId] = React.useState<string>();
  const [selectedExerciseType, setSelectedExerciseType] = React.useState<PlantType>();

  // Fetch user profile to determine available exercise types
  const { data: userProfile } = useQuery({
    queryKey: ['user-profile'],
    queryFn: budgetApi.getUserProfile,
  });

  // Fetch plants based on exercise type
  const { data: plants, isLoading: plantsLoading } = useQuery({
    queryKey: ['plants', selectedExerciseType],
    queryFn: () => budgetApi.getPlants(selectedExerciseType),
    enabled: !!selectedExerciseType,
  });

  // Fetch production units for selected plant
  const { data: productionUnits, isLoading: unitsLoading } = useQuery({
    queryKey: ['production-units', selectedPlantId],
    queryFn: () => budgetApi.getProductionUnits(selectedPlantId),
    enabled: !!selectedPlantId,
  });

  // Create budget mutation
  const createBudgetMutation = useMutation({
    mutationFn: (data: CreateBudgetRequest) => budgetApi.createBudget(data),
    onSuccess: (response) => {
      message.success('Budget creato con successo!');
      queryClient.invalidateQueries({ queryKey: ['budgets'] });
      form.resetFields();
      onSuccess?.(response.budgetId);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.detail || 'Errore durante la creazione del budget';
      message.error(errorMessage);
    },
  });

  const handleExerciseTypeChange = (exerciseType: PlantType) => {
    setSelectedExerciseType(exerciseType);
    setSelectedPlantId(undefined);
    form.setFieldsValue({
      plantId: undefined,
      productionUnitId: undefined
    });
  };

  const handlePlantChange = (plantId: string) => {
    setSelectedPlantId(plantId);
    form.setFieldValue('productionUnitId', undefined);
  };

  const handleSubmit = (values: FormValues) => {
    createBudgetMutation.mutate(values);
  };

  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear + i - 1);

  // Determine available exercise types based on user profile
  const getAvailableExerciseTypes = () => {
    if (!userProfile) return [];

    const types = [];
    if (userProfile.userType === UserType.Headquarters || userProfile.userType === UserType.Cross) {
      types.push(PlantType.Thermal, PlantType.Hydroelectric);
    } else if (userProfile.userType === UserType.Thermal) {
      types.push(PlantType.Thermal);
    } else if (userProfile.userType === UserType.Hydroelectric) {
      types.push(PlantType.Hydroelectric);
    }
    return types;
  };

  const availableExerciseTypes = getAvailableExerciseTypes();
  const shouldShowExerciseType = availableExerciseTypes.length > 1;

  // Auto-select exercise type if user has access to only one type
  React.useEffect(() => {
    if (availableExerciseTypes.length === 1 && !selectedExerciseType) {
      const autoType = availableExerciseTypes[0];
      setSelectedExerciseType(autoType);
      form.setFieldValue('exerciseType', autoType);
    }
  }, [availableExerciseTypes, selectedExerciseType, form]);

  return (
    <Card title="Crea Nuovo Budget Annuale" style={{ maxWidth: 600, margin: '0 auto' }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          year: currentYear + 1,
          exerciseType: availableExerciseTypes.length === 1 ? availableExerciseTypes[0] : undefined,
        }}
      >
        <Form.Item
          name="year"
          label="Anno"
          rules={[{ required: true, message: 'Seleziona un anno' }]}
        >
          <Select placeholder="Seleziona anno">
            {yearOptions.map(year => (
              <Select.Option key={year} value={year}>
                {year}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {shouldShowExerciseType && (
          <Form.Item
            name="exerciseType"
            label="Tipologia Esercizio"
            rules={[{ required: true, message: 'Seleziona una tipologia di esercizio' }]}
          >
            <Select
              placeholder="Seleziona tipologia esercizio"
              onChange={handleExerciseTypeChange}
            >
              {availableExerciseTypes.map(type => (
                <Select.Option key={type} value={type}>
                  {type === PlantType.Thermal ? 'Termoelettrico' : 'Idroelettrico'}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        )}

        <Form.Item
          name="plantId"
          label="Impianto"
          rules={[{ required: true, message: 'Seleziona un impianto' }]}
        >
          <Select
            placeholder="Seleziona impianto"
            loading={plantsLoading}
            onChange={handlePlantChange}
            disabled={shouldShowExerciseType && !selectedExerciseType}
            showSearch
            optionFilterProp="children"
          >
            {plants?.map(plant => (
              <Select.Option key={plant.id} value={plant.id}>
                {plant.name} ({plant.type === PlantType.Thermal ? 'Termo' : 'Idro'})
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="productionUnitId"
          label="Unità Produttiva"
          rules={[{ required: true, message: 'Seleziona un\'unità produttiva' }]}
        >
          <Select
            placeholder="Seleziona unità produttiva"
            loading={unitsLoading}
            disabled={!selectedPlantId}
            showSearch
            optionFilterProp="children"
          >
            {productionUnits?.map(unit => (
              <Select.Option key={unit.id} value={unit.id}>
                {unit.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={createBudgetMutation.isPending}
              size="large"
            >
              Crea Budget
            </Button>
            {onCancel && (
              <Button onClick={onCancel} size="large">
                Annulla
              </Button>
            )}
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};
