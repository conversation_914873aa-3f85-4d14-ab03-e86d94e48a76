import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, Spin, Alert, Button, Space, Typography, Descriptions, Tag } from 'antd';
import { ArrowLeftOutlined, LinkOutlined, SwapOutlined } from '@ant-design/icons';
import { budgetApi } from '../services/budgetApi';
import { BudgetGrid } from './BudgetGrid';
import { BudgetSummary } from './BudgetSummary';
import { MaintenancePlanAssociation } from './MaintenancePlanAssociation';
import { BudgetWorkflow } from './BudgetWorkflow';
import { BudgetComparison } from './BudgetComparison';
import { BudgetGridRow, BudgetStatus } from '../types/budget';

const { Title } = Typography;

export const BudgetDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [maintenancePlanModalVisible, setMaintenancePlanModalVisible] = React.useState(false);
  const [comparisonModalVisible, setComparisonModalVisible] = React.useState(false);

  const { data: budget, isLoading, error } = useQuery({
    queryKey: ['budget', id],
    queryFn: () => budgetApi.getBudget(id!),
    enabled: !!id,
  });

  const { data: kpis } = useQuery({
    queryKey: ['kpis'],
    queryFn: budgetApi.getKPIs,
  });

  const [gridData, setGridData] = React.useState<BudgetGridRow[]>([]);

  // Convert budget data to grid format
  React.useEffect(() => {
    if (budget && kpis) {
      const gridRows: BudgetGridRow[] = kpis.map(kpi => {
        const kpiItems = budget.items.filter(item => item.kpiId === kpi.id);
        const monthlyValues = Array.from({ length: 12 }, (_, month) => {
          const item = kpiItems.find(item => item.month === month);
          return {
            month,
            proposedValue: item?.proposedValue,
            budgetValue: item?.budgetValue,
          };
        });

        return {
          kpi,
          isAutoCalculated: kpi.isAutoCalculated,
          monthlyValues,
        };
      });

      setGridData(gridRows);
    }
  }, [budget, kpis]);

  const saveMutation = useMutation({
    mutationFn: () => {
      // Convert grid data back to budget items
      const items = gridData.flatMap(row =>
        row.monthlyValues
          .filter(mv => mv.proposedValue !== undefined || mv.budgetValue !== undefined)
          .map(mv => ({
            id: `${row.kpi.id}-${mv.month}`,
            kpiId: row.kpi.id,
            month: mv.month,
            proposedValue: mv.proposedValue,
            budgetValue: mv.budgetValue,
            createdAt: new Date().toISOString(),
          }))
      );

      return budgetApi.updateBudget(id!, { items });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['budget', id] });
    },
  });

  const copyFromProposalMutation = useMutation({
    mutationFn: () => budgetApi.copyFromProposal(id!),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['budget', id] });
    },
  });

  const calculateAutomaticMutation = useMutation({
    mutationFn: () => budgetApi.calculateAutomatic(id!),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['budget', id] });
    },
  });

  const calculateDerivedMutation = useMutation({
    mutationFn: () => budgetApi.calculateDerived(id!),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['budget', id] });
    },
  });

  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error || !budget) {
    return (
      <Alert
        message="Errore"
        description="Impossibile caricare il budget"
        type="error"
        showIcon
      />
    );
  }

  const isReadOnly = budget.status !== BudgetStatus.Draft;

  const getStatusColor = (status: BudgetStatus) => {
    switch (status) {
      case BudgetStatus.Draft: return 'default';
      case BudgetStatus.Submitted: return 'processing';
      case BudgetStatus.ApprovedPlant: return 'success';
      case BudgetStatus.InApprovalHeadquarters: return 'warning';
      case BudgetStatus.ApprovedHeadquarters: return 'success';
      case BudgetStatus.Reference: return 'purple';
      case BudgetStatus.Rejected: return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: BudgetStatus) => {
    switch (status) {
      case BudgetStatus.Draft: return 'Bozza';
      case BudgetStatus.Submitted: return 'Inviato';
      case BudgetStatus.ApprovedPlant: return 'Approvato Impianto';
      case BudgetStatus.InApprovalHeadquarters: return 'In Approvazione Sede';
      case BudgetStatus.ApprovedHeadquarters: return 'Approvato Sede';
      case BudgetStatus.Reference: return 'Di Riferimento';
      case BudgetStatus.Rejected: return 'Rifiutato';
      default: return status;
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card>
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Space>
                <Button 
                  icon={<ArrowLeftOutlined />} 
                  onClick={() => navigate('/budgets')}
                >
                  Torna alla lista
                </Button>
                <Title level={3} style={{ margin: 0 }}>
                  Budget {budget.year}
                </Title>
              </Space>
              <Space>
                <Button
                  icon={<SwapOutlined />}
                  onClick={() => setComparisonModalVisible(true)}
                >
                  Confronta
                </Button>
                <Tag color={getStatusColor(budget.status)}>
                  {getStatusText(budget.status)}
                </Tag>
              </Space>
            </div>

            <Descriptions column={3} bordered size="small">
              <Descriptions.Item label="Anno">{budget.year}</Descriptions.Item>
              <Descriptions.Item label="Impianto">
                {/* TODO: Get plant name from plantId */}
                {budget.plantId}
              </Descriptions.Item>
              <Descriptions.Item label="Unità Produttiva">
                {/* TODO: Get production unit name from productionUnitId */}
                {budget.productionUnitId}
              </Descriptions.Item>
              <Descriptions.Item label="Versione">{budget.version}</Descriptions.Item>
              <Descriptions.Item label="Creato da">{budget.createdBy}</Descriptions.Item>
              <Descriptions.Item label="Data creazione">
                {new Date(budget.createdAt).toLocaleDateString('it-IT')}
              </Descriptions.Item>
              <Descriptions.Item label="Piano Manutenzione" span={2}>
                {budget.maintenancePlanId ? (
                  <Space>
                    <Text>{budget.maintenancePlanId}</Text>
                    <Button
                      type="link"
                      size="small"
                      icon={<LinkOutlined />}
                      onClick={() => setMaintenancePlanModalVisible(true)}
                    >
                      Cambia
                    </Button>
                  </Space>
                ) : (
                  <Space>
                    <Text type="secondary">Nessun piano associato</Text>
                    <Button
                      type="link"
                      size="small"
                      icon={<LinkOutlined />}
                      onClick={() => setMaintenancePlanModalVisible(true)}
                    >
                      Associa
                    </Button>
                  </Space>
                )}
              </Descriptions.Item>
            </Descriptions>
          </Space>
        </Card>

        <BudgetWorkflow
          budgetId={budget.id}
          currentStatus={budget.status}
          year={budget.year}
          plantName={budget.plantId} // TODO: Get actual plant name
          productionUnitName={budget.productionUnitId} // TODO: Get actual production unit name
          onStatusChange={(newStatus) => {
            // Update local state or refetch budget data
            queryClient.invalidateQueries({ queryKey: ['budget', id] });
          }}
        />

        <BudgetSummary
          data={gridData}
          year={budget.year}
        />

        <BudgetGrid
          budgetId={budget.id}
          year={budget.year}
          data={gridData}
          readOnly={isReadOnly}
          onDataChange={setGridData}
          onSave={() => saveMutation.mutate()}
          onCopyFromProposal={() => copyFromProposalMutation.mutate()}
          onCalculateAutomatic={() => calculateAutomaticMutation.mutate()}
          onCalculateDerived={() => calculateDerivedMutation.mutate()}
        />

        <MaintenancePlanAssociation
          budgetId={budget.id}
          plantId={budget.plantId}
          year={budget.year}
          currentPlanId={budget.maintenancePlanId}
          visible={maintenancePlanModalVisible}
          onClose={() => setMaintenancePlanModalVisible(false)}
          onAssociate={(planId) => {
            // Update local state or refetch budget data
            queryClient.invalidateQueries({ queryKey: ['budget', id] });
          }}
        />

        <BudgetComparison
          currentBudget={budget}
          visible={comparisonModalVisible}
          onClose={() => setComparisonModalVisible(false)}
        />
      </Space>
    </div>
  );
};
