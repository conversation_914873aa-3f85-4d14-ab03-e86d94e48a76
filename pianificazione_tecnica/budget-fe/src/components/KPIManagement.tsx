import React from 'react';
import { Table, Button, Space, Card, Typography, Tag, Modal, Form, Input, Select, Switch, ColorPicker, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { budgetApi } from '../services/budgetApi';
import { KPI, KPICategory } from '../types/budget';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;
const { TextArea } = Input;

interface KPIFormData {
  code: string;
  name: string;
  unitOfMeasure: string;
  categoryId: string;
  isAutoCalculated: boolean;
  calculationFormula?: string;
  displayOrder: number;
}

interface CategoryFormData {
  name: string;
  color: string;
  displayOrder: number;
}

export const KPIManagement: React.FC = () => {
  const queryClient = useQueryClient();
  const [kpiModalVisible, setKpiModalVisible] = React.useState(false);
  const [categoryModalVisible, setCategoryModalVisible] = React.useState(false);
  const [editingKpi, setEditingKpi] = React.useState<KPI | null>(null);
  const [editingCategory, setEditingCategory] = React.useState<KPICategory | null>(null);
  const [kpiForm] = Form.useForm<KPIFormData>();
  const [categoryForm] = Form.useForm<CategoryFormData>();

  const { data: kpis, isLoading: kpisLoading } = useQuery({
    queryKey: ['kpis'],
    queryFn: budgetApi.getKPIs,
  });

  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['kpi-categories'],
    queryFn: budgetApi.getKPICategories,
  });

  const saveKpiMutation = useMutation({
    mutationFn: (data: KPIFormData) => {
      if (editingKpi) {
        return budgetApi.updateKPI(editingKpi.id, data);
      } else {
        return budgetApi.createKPI(data);
      }
    },
    onSuccess: () => {
      message.success(editingKpi ? 'KPI aggiornato con successo' : 'KPI creato con successo');
      queryClient.invalidateQueries({ queryKey: ['kpis'] });
      setKpiModalVisible(false);
      setEditingKpi(null);
      kpiForm.resetFields();
    },
    onError: () => {
      message.error('Errore durante il salvataggio del KPI');
    },
  });

  const saveCategoryMutation = useMutation({
    mutationFn: (data: CategoryFormData) => {
      if (editingCategory) {
        return budgetApi.updateKPICategory(editingCategory.id, data);
      } else {
        return budgetApi.createKPICategory(data);
      }
    },
    onSuccess: () => {
      message.success(editingCategory ? 'Categoria aggiornata con successo' : 'Categoria creata con successo');
      queryClient.invalidateQueries({ queryKey: ['kpi-categories'] });
      setCategoryModalVisible(false);
      setEditingCategory(null);
      categoryForm.resetFields();
    },
    onError: () => {
      message.error('Errore durante il salvataggio della categoria');
    },
  });

  const deleteKpiMutation = useMutation({
    mutationFn: (id: string) => budgetApi.deleteKPI(id),
    onSuccess: () => {
      message.success('KPI eliminato con successo');
      queryClient.invalidateQueries({ queryKey: ['kpis'] });
    },
    onError: () => {
      message.error('Errore durante l\'eliminazione del KPI');
    },
  });

  const deleteCategoryMutation = useMutation({
    mutationFn: (id: string) => budgetApi.deleteKPICategory(id),
    onSuccess: () => {
      message.success('Categoria eliminata con successo');
      queryClient.invalidateQueries({ queryKey: ['kpi-categories'] });
    },
    onError: () => {
      message.error('Errore durante l\'eliminazione della categoria');
    },
  });

  const handleEditKpi = (kpi: KPI) => {
    setEditingKpi(kpi);
    kpiForm.setFieldsValue({
      code: kpi.code,
      name: kpi.name,
      unitOfMeasure: kpi.unitOfMeasure,
      categoryId: kpi.category.id,
      isAutoCalculated: kpi.isAutoCalculated,
      calculationFormula: kpi.calculationFormula,
      displayOrder: kpi.displayOrder,
    });
    setKpiModalVisible(true);
  };

  const handleEditCategory = (category: KPICategory) => {
    setEditingCategory(category);
    categoryForm.setFieldsValue({
      name: category.name,
      color: category.color,
      displayOrder: category.displayOrder,
    });
    setCategoryModalVisible(true);
  };

  const handleDeleteKpi = (kpi: KPI) => {
    Modal.confirm({
      title: 'Conferma eliminazione',
      content: `Sei sicuro di voler eliminare il KPI "${kpi.name}"?`,
      onOk: () => deleteKpiMutation.mutate(kpi.id),
    });
  };

  const handleDeleteCategory = (category: KPICategory) => {
    Modal.confirm({
      title: 'Conferma eliminazione',
      content: `Sei sicuro di voler eliminare la categoria "${category.name}"?`,
      onOk: () => deleteCategoryMutation.mutate(category.id),
    });
  };

  const kpiColumns: ColumnsType<KPI> = [
    {
      title: 'Codice',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: 'Nome',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Unità di Misura',
      dataIndex: 'unitOfMeasure',
      key: 'unitOfMeasure',
      width: 120,
    },
    {
      title: 'Categoria',
      key: 'category',
      width: 150,
      render: (_, record) => (
        <Tag color={record.category.color}>
          {record.category.name}
        </Tag>
      ),
    },
    {
      title: 'Auto Calcolato',
      dataIndex: 'isAutoCalculated',
      key: 'isAutoCalculated',
      width: 120,
      render: (value: boolean) => (
        <Tag color={value ? 'green' : 'default'}>
          {value ? 'Sì' : 'No'}
        </Tag>
      ),
    },
    {
      title: 'Ordine',
      dataIndex: 'displayOrder',
      key: 'displayOrder',
      width: 80,
    },
    {
      title: 'Azioni',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditKpi(record)}
            title="Modifica"
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteKpi(record)}
            title="Elimina"
          />
        </Space>
      ),
    },
  ];

  const categoryColumns: ColumnsType<KPICategory> = [
    {
      title: 'Nome',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Colore',
      key: 'color',
      width: 100,
      render: (_, record) => (
        <div
          style={{
            width: 20,
            height: 20,
            backgroundColor: record.color,
            border: '1px solid #ccc',
            borderRadius: 4,
          }}
        />
      ),
    },
    {
      title: 'Ordine',
      dataIndex: 'displayOrder',
      key: 'displayOrder',
      width: 80,
    },
    {
      title: 'Azioni',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditCategory(record)}
            title="Modifica"
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteCategory(record)}
            title="Elimina"
          />
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={4}>Gestione Categorie KPI</Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingCategory(null);
                categoryForm.resetFields();
                setCategoryModalVisible(true);
              }}
            >
              Nuova Categoria
            </Button>
          </div>
          <Table
            columns={categoryColumns}
            dataSource={categories}
            rowKey="id"
            loading={categoriesLoading}
            pagination={false}
            size="small"
          />
        </Card>

        <Card>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={4}>Gestione KPI</Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingKpi(null);
                kpiForm.resetFields();
                setKpiModalVisible(true);
              }}
            >
              Nuovo KPI
            </Button>
          </div>
          <Table
            columns={kpiColumns}
            dataSource={kpis}
            rowKey="id"
            loading={kpisLoading}
            pagination={{ pageSize: 10 }}
            size="small"
          />
        </Card>
      </Space>

      {/* KPI Modal */}
      <Modal
        title={editingKpi ? 'Modifica KPI' : 'Nuovo KPI'}
        open={kpiModalVisible}
        onCancel={() => {
          setKpiModalVisible(false);
          setEditingKpi(null);
          kpiForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={kpiForm}
          layout="vertical"
          onFinish={(values) => saveKpiMutation.mutate(values)}
        >
          <Form.Item
            name="code"
            label="Codice"
            rules={[{ required: true, message: 'Inserisci il codice' }]}
          >
            <Input placeholder="Es. PROD_001" />
          </Form.Item>

          <Form.Item
            name="name"
            label="Nome"
            rules={[{ required: true, message: 'Inserisci il nome' }]}
          >
            <Input placeholder="Es. Energia Elettrica Prodotta" />
          </Form.Item>

          <Form.Item
            name="unitOfMeasure"
            label="Unità di Misura"
            rules={[{ required: true, message: 'Inserisci l\'unità di misura' }]}
          >
            <Input placeholder="Es. MWh, €, h" />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="Categoria"
            rules={[{ required: true, message: 'Seleziona una categoria' }]}
          >
            <Select placeholder="Seleziona categoria">
              {categories?.map(cat => (
                <Select.Option key={cat.id} value={cat.id}>
                  {cat.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="isAutoCalculated"
            label="Calcolo Automatico"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="calculationFormula"
            label="Formula di Calcolo"
            tooltip="Utilizzata solo per KPI con calcolo automatico"
          >
            <TextArea rows={3} placeholder="Es. PROD_001 * 0.15" />
          </Form.Item>

          <Form.Item
            name="displayOrder"
            label="Ordine di Visualizzazione"
            rules={[{ required: true, message: 'Inserisci l\'ordine' }]}
          >
            <Input type="number" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={saveKpiMutation.isPending}>
                {editingKpi ? 'Aggiorna' : 'Crea'}
              </Button>
              <Button onClick={() => setKpiModalVisible(false)}>
                Annulla
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Category Modal */}
      <Modal
        title={editingCategory ? 'Modifica Categoria' : 'Nuova Categoria'}
        open={categoryModalVisible}
        onCancel={() => {
          setCategoryModalVisible(false);
          setEditingCategory(null);
          categoryForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={categoryForm}
          layout="vertical"
          onFinish={(values) => saveCategoryMutation.mutate(values)}
        >
          <Form.Item
            name="name"
            label="Nome"
            rules={[{ required: true, message: 'Inserisci il nome' }]}
          >
            <Input placeholder="Es. Produzione" />
          </Form.Item>

          <Form.Item
            name="color"
            label="Colore"
            rules={[{ required: true, message: 'Seleziona un colore' }]}
          >
            <ColorPicker showText />
          </Form.Item>

          <Form.Item
            name="displayOrder"
            label="Ordine di Visualizzazione"
            rules={[{ required: true, message: 'Inserisci l\'ordine' }]}
          >
            <Input type="number" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={saveCategoryMutation.isPending}>
                {editingCategory ? 'Aggiorna' : 'Crea'}
              </Button>
              <Button onClick={() => setCategoryModalVisible(false)}>
                Annulla
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
