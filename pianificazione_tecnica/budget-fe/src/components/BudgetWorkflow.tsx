import React from 'react';
import { Card, Button, Space, Steps, Modal, Form, Input, message, Typography, Alert, Descriptions } from 'antd';
import { 
  SendOutlined, 
  CheckOutlined, 
  CloseOutlined, 
  StarOutlined,
  ExclamationCircleOutlined 
} from '@ant-design/icons';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { budgetApi } from '../services/budgetApi';
import { BudgetStatus } from '../types/budget';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface BudgetWorkflowProps {
  budgetId: string;
  currentStatus: BudgetStatus;
  year: number;
  plantName?: string;
  productionUnitName?: string;
  onStatusChange?: (newStatus: BudgetStatus) => void;
}

interface WorkflowAction {
  key: string;
  label: string;
  icon: React.ReactNode;
  type: 'primary' | 'default' | 'dashed' | 'link' | 'text';
  danger?: boolean;
  requiresConfirmation?: boolean;
  requiresReason?: boolean;
  description: string;
}

export const BudgetWorkflow: React.FC<BudgetWorkflowProps> = ({
  budgetId,
  currentStatus,
  year,
  plantName,
  productionUnitName,
  onStatusChange,
}) => {
  const queryClient = useQueryClient();
  const [actionModalVisible, setActionModalVisible] = React.useState(false);
  const [selectedAction, setSelectedAction] = React.useState<WorkflowAction | null>(null);
  const [form] = Form.useForm();

  const submitMutation = useMutation({
    mutationFn: () => budgetApi.submitBudget(budgetId),
    onSuccess: () => {
      message.success('Budget inviato per approvazione');
      onStatusChange?.(BudgetStatus.Submitted);
      queryClient.invalidateQueries({ queryKey: ['budget', budgetId] });
    },
  });

  const approvePlantMutation = useMutation({
    mutationFn: () => budgetApi.approveBudgetAtPlant(budgetId),
    onSuccess: () => {
      message.success('Budget approvato a livello impianto');
      onStatusChange?.(BudgetStatus.ApprovedPlant);
      queryClient.invalidateQueries({ queryKey: ['budget', budgetId] });
    },
  });

  const approveHeadquartersMutation = useMutation({
    mutationFn: () => budgetApi.approveBudgetAtHeadquarters(budgetId),
    onSuccess: () => {
      message.success('Budget approvato a livello sede');
      onStatusChange?.(BudgetStatus.ApprovedHeadquarters);
      queryClient.invalidateQueries({ queryKey: ['budget', budgetId] });
    },
  });

  const rejectMutation = useMutation({
    mutationFn: (reason: string) => budgetApi.rejectBudget(budgetId, reason),
    onSuccess: () => {
      message.success('Budget rifiutato');
      onStatusChange?.(BudgetStatus.Rejected);
      queryClient.invalidateQueries({ queryKey: ['budget', budgetId] });
    },
  });

  const setReferenceMutation = useMutation({
    mutationFn: () => budgetApi.setAsReference(budgetId),
    onSuccess: () => {
      message.success('Budget impostato come riferimento');
      onStatusChange?.(BudgetStatus.Reference);
      queryClient.invalidateQueries({ queryKey: ['budget', budgetId] });
    },
  });

  const getAvailableActions = (): WorkflowAction[] => {
    const actions: WorkflowAction[] = [];

    switch (currentStatus) {
      case BudgetStatus.Draft:
        actions.push({
          key: 'submit',
          label: 'Invia per Approvazione',
          icon: <SendOutlined />,
          type: 'primary',
          requiresConfirmation: true,
          description: 'Invia il budget per l\'approvazione a livello impianto',
        });
        break;

      case BudgetStatus.Submitted:
        actions.push(
          {
            key: 'approve-plant',
            label: 'Approva Impianto',
            icon: <CheckOutlined />,
            type: 'primary',
            requiresConfirmation: true,
            description: 'Approva il budget a livello impianto',
          },
          {
            key: 'reject',
            label: 'Rifiuta',
            icon: <CloseOutlined />,
            type: 'default',
            danger: true,
            requiresConfirmation: true,
            requiresReason: true,
            description: 'Rifiuta il budget e riporta in bozza',
          }
        );
        break;

      case BudgetStatus.ApprovedPlant:
        actions.push(
          {
            key: 'approve-headquarters',
            label: 'Approva Sede',
            icon: <CheckOutlined />,
            type: 'primary',
            requiresConfirmation: true,
            description: 'Approva il budget a livello sede',
          },
          {
            key: 'reject',
            label: 'Rifiuta',
            icon: <CloseOutlined />,
            type: 'default',
            danger: true,
            requiresConfirmation: true,
            requiresReason: true,
            description: 'Rifiuta il budget e riporta in bozza',
          }
        );
        break;

      case BudgetStatus.ApprovedHeadquarters:
        actions.push({
          key: 'set-reference',
          label: 'Imposta come Riferimento',
          icon: <StarOutlined />,
          type: 'primary',
          requiresConfirmation: true,
          description: 'Imposta questo budget come riferimento per l\'anno',
        });
        break;

      default:
        break;
    }

    return actions;
  };

  const handleAction = (action: WorkflowAction) => {
    if (action.requiresConfirmation || action.requiresReason) {
      setSelectedAction(action);
      setActionModalVisible(true);
    } else {
      executeAction(action.key);
    }
  };

  const executeAction = async (actionKey: string, reason?: string) => {
    try {
      switch (actionKey) {
        case 'submit':
          await submitMutation.mutateAsync();
          break;
        case 'approve-plant':
          await approvePlantMutation.mutateAsync();
          break;
        case 'approve-headquarters':
          await approveHeadquartersMutation.mutateAsync();
          break;
        case 'reject':
          if (reason) {
            await rejectMutation.mutateAsync(reason);
          }
          break;
        case 'set-reference':
          await setReferenceMutation.mutateAsync();
          break;
      }
      setActionModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('Errore durante l\'esecuzione dell\'azione');
    }
  };

  const getStatusStep = () => {
    switch (currentStatus) {
      case BudgetStatus.Draft:
        return 0;
      case BudgetStatus.Submitted:
        return 1;
      case BudgetStatus.ApprovedPlant:
        return 2;
      case BudgetStatus.ApprovedHeadquarters:
        return 3;
      case BudgetStatus.Reference:
        return 4;
      case BudgetStatus.Rejected:
        return -1;
      default:
        return 0;
    }
  };

  const getStatusText = (status: BudgetStatus) => {
    switch (status) {
      case BudgetStatus.Draft: return 'Bozza';
      case BudgetStatus.Submitted: return 'Inviato';
      case BudgetStatus.ApprovedPlant: return 'Approvato Impianto';
      case BudgetStatus.ApprovedHeadquarters: return 'Approvato Sede';
      case BudgetStatus.Reference: return 'Di Riferimento';
      case BudgetStatus.Rejected: return 'Rifiutato';
      default: return status;
    }
  };

  const availableActions = getAvailableActions();
  const currentStep = getStatusStep();

  return (
    <Card title="Workflow Approvazione" size="small">
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {currentStatus === BudgetStatus.Rejected && (
          <Alert
            message="Budget Rifiutato"
            description="Questo budget è stato rifiutato e deve essere modificato prima di poter essere inviato nuovamente."
            type="error"
            showIcon
          />
        )}

        <Steps
          current={currentStep}
          status={currentStatus === BudgetStatus.Rejected ? 'error' : 'process'}
          size="small"
          items={[
            {
              title: 'Bozza',
              description: 'Creazione e modifica',
            },
            {
              title: 'Inviato',
              description: 'In attesa approvazione impianto',
            },
            {
              title: 'Approvato Impianto',
              description: 'In attesa approvazione sede',
            },
            {
              title: 'Approvato Sede',
              description: 'Approvazione completata',
            },
            {
              title: 'Riferimento',
              description: 'Budget di riferimento',
            },
          ]}
        />

        <Descriptions column={1} size="small" bordered>
          <Descriptions.Item label="Stato Attuale">
            {getStatusText(currentStatus)}
          </Descriptions.Item>
          <Descriptions.Item label="Anno">{year}</Descriptions.Item>
          {plantName && (
            <Descriptions.Item label="Impianto">{plantName}</Descriptions.Item>
          )}
          {productionUnitName && (
            <Descriptions.Item label="Unità Produttiva">{productionUnitName}</Descriptions.Item>
          )}
        </Descriptions>

        {availableActions.length > 0 && (
          <div>
            <Title level={5}>Azioni Disponibili</Title>
            <Space wrap>
              {availableActions.map(action => (
                <Button
                  key={action.key}
                  type={action.type}
                  icon={action.icon}
                  danger={action.danger}
                  onClick={() => handleAction(action)}
                  loading={
                    (action.key === 'submit' && submitMutation.isPending) ||
                    (action.key === 'approve-plant' && approvePlantMutation.isPending) ||
                    (action.key === 'approve-headquarters' && approveHeadquartersMutation.isPending) ||
                    (action.key === 'reject' && rejectMutation.isPending) ||
                    (action.key === 'set-reference' && setReferenceMutation.isPending)
                  }
                >
                  {action.label}
                </Button>
              ))}
            </Space>
          </div>
        )}
      </Space>

      <Modal
        title={selectedAction?.label}
        open={actionModalVisible}
        onCancel={() => {
          setActionModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        {selectedAction && (
          <Form
            form={form}
            layout="vertical"
            onFinish={(values) => {
              executeAction(selectedAction.key, values.reason);
            }}
          >
            <Alert
              message={selectedAction.description}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            {selectedAction.requiresReason && (
              <Form.Item
                name="reason"
                label="Motivo"
                rules={[{ required: true, message: 'Inserisci il motivo' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="Inserisci il motivo del rifiuto..."
                />
              </Form.Item>
            )}

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  danger={selectedAction.danger}
                  icon={selectedAction.icon}
                >
                  Conferma
                </Button>
                <Button onClick={() => setActionModalVisible(false)}>
                  Annulla
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </Card>
  );
};
