import { BudgetGridRow, KPI, MonthlyBudgetValue } from '../types/budget';

export class CalculationService {
  /**
   * Copia i valori dalla colonna "Proposta" alla colonna "Obiettivo" per tutti i KPI
   */
  static copyFromProposal(gridData: BudgetGridRow[]): BudgetGridRow[] {
    return gridData.map(row => ({
      ...row,
      monthlyValues: row.monthlyValues.map(mv => ({
        ...mv,
        budgetValue: mv.proposedValue,
      })),
    }));
  }

  /**
   * Ricalcola i KPI automatici basandosi sulle formule definite
   */
  static calculateAutomatic(gridData: BudgetGridRow[]): BudgetGridRow[] {
    const newData = [...gridData];
    
    // Prima passa: identifica i KPI automatici
    const autoKpis = newData.filter(row => row.isAutoCalculated);
    
    // Seconda passa: calcola i valori per ogni KPI automatico
    autoKpis.forEach(autoRow => {
      if (autoRow.kpi.calculationFormula) {
        autoRow.monthlyValues = autoRow.monthlyValues.map(mv => ({
          ...mv,
          proposedValue: this.evaluateFormula(
            autoRow.kpi.calculationFormula!,
            newData,
            mv.month,
            'proposedValue'
          ),
          budgetValue: this.evaluateFormula(
            autoRow.kpi.calculationFormula!,
            newData,
            mv.month,
            'budgetValue'
          ),
        }));
      }
    });

    return newData;
  }

  /**
   * Calcola i KPI derivati (quelli che dipendono da altri KPI)
   */
  static calculateDerived(gridData: BudgetGridRow[]): BudgetGridRow[] {
    const newData = [...gridData];
    
    // Identifica i KPI derivati (quelli con formule che referenziano altri KPI)
    const derivedKpis = newData.filter(row => 
      row.kpi.calculationFormula && 
      this.hasKpiReferences(row.kpi.calculationFormula)
    );

    // Calcola i valori derivati
    derivedKpis.forEach(derivedRow => {
      if (derivedRow.kpi.calculationFormula) {
        derivedRow.monthlyValues = derivedRow.monthlyValues.map(mv => ({
          ...mv,
          proposedValue: this.evaluateFormula(
            derivedRow.kpi.calculationFormula!,
            newData,
            mv.month,
            'proposedValue'
          ),
          budgetValue: this.evaluateFormula(
            derivedRow.kpi.calculationFormula!,
            newData,
            mv.month,
            'budgetValue'
          ),
        }));
      }
    });

    return newData;
  }

  /**
   * Valuta una formula matematica sostituendo i riferimenti ai KPI con i valori effettivi
   */
  private static evaluateFormula(
    formula: string,
    gridData: BudgetGridRow[],
    month: number,
    valueType: 'proposedValue' | 'budgetValue'
  ): number | undefined {
    try {
      let evaluatedFormula = formula;

      // Sostituisce i riferimenti ai KPI con i valori effettivi
      const kpiReferences = formula.match(/[A-Z_]+_\d+/g) || [];
      
      kpiReferences.forEach(kpiCode => {
        const referencedRow = gridData.find(row => row.kpi.code === kpiCode);
        if (referencedRow) {
          const monthlyValue = referencedRow.monthlyValues.find(mv => mv.month === month);
          const value = monthlyValue?.[valueType] || 0;
          evaluatedFormula = evaluatedFormula.replace(new RegExp(kpiCode, 'g'), value.toString());
        }
      });

      // Valuta l'espressione matematica
      return this.safeEval(evaluatedFormula);
    } catch (error) {
      console.error('Error evaluating formula:', formula, error);
      return undefined;
    }
  }

  /**
   * Verifica se una formula contiene riferimenti ad altri KPI
   */
  private static hasKpiReferences(formula: string): boolean {
    return /[A-Z_]+_\d+/.test(formula);
  }

  /**
   * Valutazione sicura di espressioni matematiche semplici
   */
  private static safeEval(expression: string): number | undefined {
    try {
      // Rimuove spazi e verifica che contenga solo caratteri sicuri
      const cleanExpression = expression.replace(/\s/g, '');
      
      if (!/^[0-9+\-*/.()]+$/.test(cleanExpression)) {
        throw new Error('Invalid characters in expression');
      }

      // Usa Function constructor per valutazione sicura
      const result = new Function(`return ${cleanExpression}`)();
      
      return typeof result === 'number' && !isNaN(result) ? result : undefined;
    } catch (error) {
      console.error('Error in safe eval:', expression, error);
      return undefined;
    }
  }

  /**
   * Calcola i totali annuali per ogni KPI
   */
  static calculateAnnualTotals(gridData: BudgetGridRow[]): { [kpiId: string]: { proposed: number; budget: number } } {
    const totals: { [kpiId: string]: { proposed: number; budget: number } } = {};

    gridData.forEach(row => {
      const proposedTotal = row.monthlyValues.reduce((sum, mv) => sum + (mv.proposedValue || 0), 0);
      const budgetTotal = row.monthlyValues.reduce((sum, mv) => sum + (mv.budgetValue || 0), 0);
      
      totals[row.kpi.id] = {
        proposed: proposedTotal,
        budget: budgetTotal,
      };
    });

    return totals;
  }

  /**
   * Valida i dati della griglia per inconsistenze
   */
  static validateGridData(gridData: BudgetGridRow[]): string[] {
    const errors: string[] = [];

    gridData.forEach(row => {
      // Verifica che i KPI automatici abbiano formule
      if (row.isAutoCalculated && !row.kpi.calculationFormula) {
        errors.push(`KPI "${row.kpi.name}" è marcato come automatico ma non ha una formula di calcolo`);
      }

      // Verifica valori negativi dove non dovrebbero esserci
      row.monthlyValues.forEach((mv, monthIndex) => {
        if (mv.proposedValue !== undefined && mv.proposedValue < 0) {
          errors.push(`KPI "${row.kpi.name}" ha un valore proposto negativo nel mese ${monthIndex + 1}`);
        }
        if (mv.budgetValue !== undefined && mv.budgetValue < 0) {
          errors.push(`KPI "${row.kpi.name}" ha un valore budget negativo nel mese ${monthIndex + 1}`);
        }
      });
    });

    return errors;
  }

  /**
   * Applica regole di business specifiche per i calcoli
   */
  static applyBusinessRules(gridData: BudgetGridRow[]): BudgetGridRow[] {
    const newData = [...gridData];

    // Esempio: Assicura che i costi di manutenzione straordinaria non superino il 20% di quella programmata
    const maintenanceProgrammed = newData.find(row => row.kpi.code === 'MAINT_001');
    const maintenanceExtraordinary = newData.find(row => row.kpi.code === 'MAINT_002');

    if (maintenanceProgrammed && maintenanceExtraordinary) {
      maintenanceExtraordinary.monthlyValues = maintenanceExtraordinary.monthlyValues.map((mv, index) => {
        const programmedValue = maintenanceProgrammed.monthlyValues[index];
        const maxExtraordinary = (programmedValue?.budgetValue || 0) * 0.2;
        
        return {
          ...mv,
          budgetValue: mv.budgetValue && mv.budgetValue > maxExtraordinary 
            ? maxExtraordinary 
            : mv.budgetValue,
        };
      });
    }

    return newData;
  }

  /**
   * Genera un report di riepilogo dei calcoli
   */
  static generateCalculationReport(gridData: BudgetGridRow[]): {
    totalProposed: number;
    totalBudget: number;
    autoCalculatedKpis: number;
    manualKpis: number;
    categoryTotals: { [categoryName: string]: { proposed: number; budget: number } };
  } {
    const totals = this.calculateAnnualTotals(gridData);
    
    const totalProposed = Object.values(totals).reduce((sum, t) => sum + t.proposed, 0);
    const totalBudget = Object.values(totals).reduce((sum, t) => sum + t.budget, 0);
    
    const autoCalculatedKpis = gridData.filter(row => row.isAutoCalculated).length;
    const manualKpis = gridData.filter(row => !row.isAutoCalculated).length;

    const categoryTotals: { [categoryName: string]: { proposed: number; budget: number } } = {};
    
    gridData.forEach(row => {
      const categoryName = row.kpi.category.name;
      if (!categoryTotals[categoryName]) {
        categoryTotals[categoryName] = { proposed: 0, budget: 0 };
      }
      
      const kpiTotals = totals[row.kpi.id];
      categoryTotals[categoryName].proposed += kpiTotals.proposed;
      categoryTotals[categoryName].budget += kpiTotals.budget;
    });

    return {
      totalProposed,
      totalBudget,
      autoCalculatedKpis,
      manualKpis,
      categoryTotals,
    };
  }
}
