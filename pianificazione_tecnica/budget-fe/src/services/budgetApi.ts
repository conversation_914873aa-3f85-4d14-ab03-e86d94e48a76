import { api } from './api';
import { Budget, CreateBudgetRequest, CreateBudgetResponse, Plant, ProductionUnit, UserProfile, PlantType, KPI, KPICategory, BudgetStatus, UserType } from '../types/budget';

export const budgetApi = {
  // Budget operations
  createBudget: (data: CreateBudgetRequest): Promise<CreateBudgetResponse> =>
    api.post('/api/v1/budget/annual', data),

  getBudget: (id: string): Promise<Budget> => {
    // Mock implementation
    const mockBudget: Budget = {
      id,
      plantId: 'plant-1',
      productionUnitId: 'unit-1',
      year: 2025,
      status: BudgetStatus.Draft,
      version: 1,
      createdBy: 'user-1',
      createdAt: '2024-01-15T10:00:00Z',
      maintenancePlanId: 'plan-2025-001',
      items: [
        // Sample budget items for different KPIs and months
        { id: 'item-1', kpiId: 'kpi-1', month: 0, proposedValue: 1000, budgetValue: 1100, createdAt: '2024-01-15T10:00:00Z' },
        { id: 'item-2', kpiId: 'kpi-1', month: 1, proposedValue: 1200, budgetValue: 1300, createdAt: '2024-01-15T10:00:00Z' },
        { id: 'item-3', kpiId: 'kpi-2', month: 0, proposedValue: 500, budgetValue: 550, createdAt: '2024-01-15T10:00:00Z' },
        { id: 'item-4', kpiId: 'kpi-3', month: 0, proposedValue: 75000, budgetValue: 80000, createdAt: '2024-01-15T10:00:00Z' },
      ],
    };
    return Promise.resolve(mockBudget);
  },

  getBudgets: (params?: { year?: number; plantId?: string; status?: string }): Promise<Budget[]> => {
    // Mock implementation
    const mockBudgets: Budget[] = [
      {
        id: 'budget-1',
        plantId: 'plant-1',
        productionUnitId: 'unit-1',
        year: 2025,
        status: BudgetStatus.Draft,
        version: 1,
        createdBy: 'user-1',
        createdAt: '2024-01-15T10:00:00Z',
        maintenancePlanId: 'plan-2025-001',
        items: [],
      },
      {
        id: 'budget-2',
        plantId: 'plant-2',
        productionUnitId: 'unit-2',
        year: 2025,
        status: BudgetStatus.Submitted,
        version: 1,
        createdBy: 'user-2',
        createdAt: '2024-01-10T09:00:00Z',
        items: [],
      },
      {
        id: 'budget-3',
        plantId: 'plant-1',
        productionUnitId: 'unit-1',
        year: 2024,
        status: BudgetStatus.Reference,
        version: 2,
        createdBy: 'user-1',
        createdAt: '2023-12-01T08:00:00Z',
        items: [],
      },
    ];

    // Apply filters
    let filteredBudgets = mockBudgets;
    if (params?.year) {
      filteredBudgets = filteredBudgets.filter(b => b.year === params.year);
    }
    if (params?.plantId) {
      filteredBudgets = filteredBudgets.filter(b => b.plantId === params.plantId);
    }
    if (params?.status) {
      filteredBudgets = filteredBudgets.filter(b => b.status === params.status);
    }

    return Promise.resolve(filteredBudgets);
  },

  updateBudget: (id: string, data: Partial<Budget>): Promise<Budget> =>
    api.put(`/api/v1/budget/${id}`, data),

  deleteBudget: (id: string): Promise<void> =>
    api.delete(`/api/v1/budget/${id}`),

  // Budget workflow operations
  submitBudget: (id: string): Promise<void> =>
    api.post(`/api/v1/budget/${id}/submit`),

  approveBudgetAtPlant: (id: string): Promise<void> =>
    api.post(`/api/v1/budget/${id}/approve-plant`),

  approveBudgetAtHeadquarters: (id: string): Promise<void> =>
    api.post(`/api/v1/budget/${id}/approve-headquarters`),

  rejectBudget: (id: string, reason: string): Promise<void> =>
    api.post(`/api/v1/budget/${id}/reject`, { reason }),

  setAsReference: (id: string): Promise<void> =>
    api.post(`/api/v1/budget/${id}/set-reference`),

  // Master data operations
  getPlants: (exerciseType?: PlantType): Promise<Plant[]> => {
    // Mock implementation - filter plants by exercise type
    const allPlants: Plant[] = [
      { id: 'plant-1', name: 'Centrale Termoelettrica Acerra', type: PlantType.Thermal, isActive: true },
      { id: 'plant-2', name: 'Centrale Idroelettrica Magisano', type: PlantType.Hydroelectric, isActive: true },
      { id: 'plant-3', name: 'Centrale Termoelettrica Brindisi', type: PlantType.Thermal, isActive: true },
      { id: 'plant-4', name: 'Centrale Idroelettrica Presenzano', type: PlantType.Hydroelectric, isActive: true },
    ];

    const filteredPlants = exerciseType
      ? allPlants.filter(plant => plant.type === exerciseType)
      : allPlants;

    return Promise.resolve(filteredPlants);
  },

  getProductionUnits: (plantId?: string): Promise<ProductionUnit[]> => {
    // Mock implementation - return production units for the selected plant
    const allUnits: ProductionUnit[] = [
      { id: 'unit-1', name: 'Acerra', plantId: 'plant-1', isActive: true },
      { id: 'unit-2', name: 'Magisano', plantId: 'plant-2', isActive: true },
      { id: 'unit-3', name: 'Brindisi Sud', plantId: 'plant-3', isActive: true },
      { id: 'unit-4', name: 'Brindisi Nord', plantId: 'plant-3', isActive: true },
      { id: 'unit-5', name: 'Presenzano', plantId: 'plant-4', isActive: true },
    ];

    const filteredUnits = plantId
      ? allUnits.filter(unit => unit.plantId === plantId)
      : allUnits;

    return Promise.resolve(filteredUnits);
  },

  // User profile operations
  getUserProfile: (): Promise<UserProfile> => {
    // Mock implementation - in real app this would call the API
    return Promise.resolve({
      id: 'user-1',
      userType: UserType.Cross, // Cross user can see both types
      hasAccessToThermal: true,
      hasAccessToHydroelectric: true,
    });
  },

  // KPI operations
  getKPIs: (): Promise<KPI[]> => {
    // Mock implementation
    const categories: KPICategory[] = [
      { id: 'cat-1', name: 'Produzione', color: '#90EE90', displayOrder: 1 },
      { id: 'cat-2', name: 'Manutenzione', color: '#FFB6C1', displayOrder: 2 },
      { id: 'cat-3', name: 'Combustibili', color: '#87CEEB', displayOrder: 3 },
      { id: 'cat-4', name: 'Personale', color: '#DDA0DD', displayOrder: 4 },
    ];

    const kpis: KPI[] = [
      {
        id: 'kpi-1',
        code: 'PROD_001',
        name: 'Energia Elettrica Prodotta',
        unitOfMeasure: 'MWh',
        category: categories[0],
        isAutoCalculated: false,
        displayOrder: 1,
      },
      {
        id: 'kpi-2',
        code: 'PROD_002',
        name: 'Ore di Funzionamento',
        unitOfMeasure: 'h',
        category: categories[0],
        isAutoCalculated: false,
        displayOrder: 2,
      },
      {
        id: 'kpi-3',
        code: 'MAINT_001',
        name: 'Costi Manutenzione Programmata',
        unitOfMeasure: '€',
        category: categories[1],
        isAutoCalculated: false,
        displayOrder: 3,
      },
      {
        id: 'kpi-4',
        code: 'MAINT_002',
        name: 'Costi Manutenzione Straordinaria',
        unitOfMeasure: '€',
        category: categories[1],
        isAutoCalculated: true,
        calculationFormula: 'MAINT_001 * 0.15',
        displayOrder: 4,
      },
      {
        id: 'kpi-5',
        code: 'FUEL_001',
        name: 'Consumo Combustibile',
        unitOfMeasure: 't',
        category: categories[2],
        isAutoCalculated: false,
        displayOrder: 5,
      },
      {
        id: 'kpi-6',
        code: 'HR_001',
        name: 'Costi Personale',
        unitOfMeasure: '€',
        category: categories[3],
        isAutoCalculated: false,
        displayOrder: 6,
      },
    ];

    return Promise.resolve(kpis);
  },

  getKPICategories: (): Promise<KPICategory[]> => {
    // Mock implementation
    const categories: KPICategory[] = [
      { id: 'cat-1', name: 'Produzione', color: '#90EE90', displayOrder: 1 },
      { id: 'cat-2', name: 'Manutenzione', color: '#FFB6C1', displayOrder: 2 },
      { id: 'cat-3', name: 'Combustibili', color: '#87CEEB', displayOrder: 3 },
      { id: 'cat-4', name: 'Personale', color: '#DDA0DD', displayOrder: 4 },
    ];
    return Promise.resolve(categories);
  },

  createKPI: (data: any): Promise<KPI> => {
    console.log('Creating KPI:', data);
    return Promise.resolve({} as KPI);
  },

  updateKPI: (id: string, data: any): Promise<KPI> => {
    console.log('Updating KPI:', id, data);
    return Promise.resolve({} as KPI);
  },

  deleteKPI: (id: string): Promise<void> => {
    console.log('Deleting KPI:', id);
    return Promise.resolve();
  },

  createKPICategory: (data: any): Promise<KPICategory> => {
    console.log('Creating KPI Category:', data);
    return Promise.resolve({} as KPICategory);
  },

  updateKPICategory: (id: string, data: any): Promise<KPICategory> => {
    console.log('Updating KPI Category:', id, data);
    return Promise.resolve({} as KPICategory);
  },

  deleteKPICategory: (id: string): Promise<void> => {
    console.log('Deleting KPI Category:', id);
    return Promise.resolve();
  },

  // Budget calculation operations
  copyFromProposal: (budgetId: string): Promise<void> => {
    // Mock implementation
    console.log('Copying from proposal for budget:', budgetId);
    return Promise.resolve();
  },

  calculateAutomatic: (budgetId: string): Promise<void> => {
    // Mock implementation
    console.log('Calculating automatic KPIs for budget:', budgetId);
    return Promise.resolve();
  },

  calculateDerived: (budgetId: string): Promise<void> => {
    // Mock implementation
    console.log('Calculating derived KPIs for budget:', budgetId);
    return Promise.resolve();
  },

  // Health check
  getHealth: (): Promise<{ status: string; timestamp: string }> =>
    api.get('/api/v1/budget/health'),
};
