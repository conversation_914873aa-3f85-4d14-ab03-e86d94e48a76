import { BudgetGridRow, BudgetItem } from '../types/budget';
import { CalculationService } from './calculationService';

export interface SaveOptions {
  validateBeforeSave?: boolean;
  applyBusinessRules?: boolean;
  calculateDerived?: boolean;
  showProgress?: boolean;
}

export interface SaveResult {
  success: boolean;
  savedItems: number;
  errors: string[];
  warnings: string[];
  validationErrors: string[];
}

export class SaveService {
  /**
   * Salva i dati della griglia budget con validazione e applicazione delle regole di business
   */
  static async saveBudgetData(
    budgetId: string,
    gridData: BudgetGridRow[],
    options: SaveOptions = {}
  ): Promise<SaveResult> {
    const {
      validateBeforeSave = true,
      applyBusinessRules = true,
      calculateDerived = true,
      showProgress = false,
    } = options;

    const result: SaveResult = {
      success: false,
      savedItems: 0,
      errors: [],
      warnings: [],
      validationErrors: [],
    };

    try {
      // Step 1: Validation
      if (validateBeforeSave) {
        const validationErrors = CalculationService.validateGridData(gridData);
        if (validationErrors.length > 0) {
          result.validationErrors = validationErrors;
          return result;
        }
      }

      // Step 2: Apply business rules
      let processedData = gridData;
      if (applyBusinessRules) {
        processedData = CalculationService.applyBusinessRules(processedData);
        result.warnings.push('Regole di business applicate');
      }

      // Step 3: Calculate derived values
      if (calculateDerived) {
        processedData = CalculationService.calculateDerived(processedData);
        result.warnings.push('Valori derivati ricalcolati');
      }

      // Step 4: Convert to budget items
      const budgetItems = this.convertGridDataToBudgetItems(processedData);

      // Step 5: Validate individual items
      const itemValidationResult = this.validateBudgetItems(budgetItems);
      if (!itemValidationResult.isValid) {
        result.errors = itemValidationResult.errors;
        return result;
      }

      // Step 6: Save to backend (mock implementation)
      const saveResult = await this.saveBudgetItems(budgetId, budgetItems, showProgress);
      
      result.success = saveResult.success;
      result.savedItems = saveResult.savedItems;
      result.errors = saveResult.errors;

      return result;
    } catch (error) {
      result.errors.push(`Errore durante il salvataggio: ${error instanceof Error ? error.message : 'Errore sconosciuto'}`);
      return result;
    }
  }

  /**
   * Converte i dati della griglia in elementi budget
   */
  private static convertGridDataToBudgetItems(gridData: BudgetGridRow[]): BudgetItem[] {
    const items: BudgetItem[] = [];

    gridData.forEach(row => {
      row.monthlyValues.forEach(mv => {
        if (mv.proposedValue !== undefined || mv.budgetValue !== undefined) {
          items.push({
            id: `${row.kpi.id}-${mv.month}`,
            kpiId: row.kpi.id,
            month: mv.month,
            proposedValue: mv.proposedValue,
            budgetValue: mv.budgetValue,
            createdAt: new Date().toISOString(),
            modifiedAt: new Date().toISOString(),
          });
        }
      });
    });

    return items;
  }

  /**
   * Valida gli elementi budget prima del salvataggio
   */
  private static validateBudgetItems(items: BudgetItem[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    items.forEach((item, index) => {
      // Valida che il mese sia valido
      if (item.month < 0 || item.month > 11) {
        errors.push(`Item ${index + 1}: Mese non valido (${item.month})`);
      }

      // Valida che almeno un valore sia presente
      if (item.proposedValue === undefined && item.budgetValue === undefined) {
        errors.push(`Item ${index + 1}: Almeno un valore (proposta o budget) deve essere specificato`);
      }

      // Valida che i valori non siano negativi (per alcuni KPI)
      if (item.proposedValue !== undefined && item.proposedValue < 0) {
        errors.push(`Item ${index + 1}: Il valore proposto non può essere negativo`);
      }

      if (item.budgetValue !== undefined && item.budgetValue < 0) {
        errors.push(`Item ${index + 1}: Il valore budget non può essere negativo`);
      }

      // Valida che i valori non siano eccessivamente grandi
      const maxValue = 999999999; // 999 milioni
      if (item.proposedValue !== undefined && item.proposedValue > maxValue) {
        errors.push(`Item ${index + 1}: Il valore proposto è troppo grande`);
      }

      if (item.budgetValue !== undefined && item.budgetValue > maxValue) {
        errors.push(`Item ${index + 1}: Il valore budget è troppo grande`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Salva gli elementi budget nel backend
   */
  private static async saveBudgetItems(
    budgetId: string,
    items: BudgetItem[],
    showProgress: boolean
  ): Promise<{ success: boolean; savedItems: number; errors: string[] }> {
    // Mock implementation - in real app this would call the API
    
    if (showProgress) {
      console.log(`Saving ${items.length} budget items...`);
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simulate some failures for demonstration
    const failureRate = 0.05; // 5% failure rate
    const errors: string[] = [];
    let savedItems = 0;

    for (let i = 0; i < items.length; i++) {
      if (Math.random() < failureRate) {
        errors.push(`Errore nel salvataggio dell'item ${i + 1}: Timeout di rete`);
      } else {
        savedItems++;
      }

      if (showProgress && (i + 1) % 10 === 0) {
        console.log(`Saved ${i + 1}/${items.length} items`);
      }
    }

    return {
      success: errors.length === 0,
      savedItems,
      errors,
    };
  }

  /**
   * Salva automaticamente i dati ogni X secondi
   */
  static createAutoSave(
    budgetId: string,
    getGridData: () => BudgetGridRow[],
    intervalSeconds: number = 30
  ): { start: () => void; stop: () => void; saveNow: () => Promise<SaveResult> } {
    let intervalId: NodeJS.Timeout | null = null;
    let lastSaveData: string | null = null;

    const saveNow = async (): Promise<SaveResult> => {
      const currentData = getGridData();
      const currentDataString = JSON.stringify(currentData);

      // Skip save if data hasn't changed
      if (currentDataString === lastSaveData) {
        return {
          success: true,
          savedItems: 0,
          errors: [],
          warnings: ['Nessuna modifica da salvare'],
          validationErrors: [],
        };
      }

      const result = await this.saveBudgetData(budgetId, currentData, {
        validateBeforeSave: false, // Skip validation for auto-save
        applyBusinessRules: false,
        calculateDerived: false,
        showProgress: false,
      });

      if (result.success) {
        lastSaveData = currentDataString;
      }

      return result;
    };

    const start = () => {
      if (intervalId) return; // Already started

      intervalId = setInterval(async () => {
        try {
          await saveNow();
        } catch (error) {
          console.error('Auto-save failed:', error);
        }
      }, intervalSeconds * 1000);
    };

    const stop = () => {
      if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
      }
    };

    return { start, stop, saveNow };
  }

  /**
   * Esporta i dati della griglia in formato CSV
   */
  static exportToCSV(gridData: BudgetGridRow[], filename: string = 'budget-export.csv'): void {
    const headers = [
      'KPI Code',
      'KPI Name',
      'Unit of Measure',
      'Category',
      'Month',
      'Proposed Value',
      'Budget Value',
    ];

    const rows: string[][] = [headers];

    gridData.forEach(row => {
      row.monthlyValues.forEach(mv => {
        if (mv.proposedValue !== undefined || mv.budgetValue !== undefined) {
          rows.push([
            row.kpi.code,
            row.kpi.name,
            row.kpi.unitOfMeasure,
            row.kpi.category.name,
            (mv.month + 1).toString(),
            mv.proposedValue?.toString() || '',
            mv.budgetValue?.toString() || '',
          ]);
        }
      });
    });

    const csvContent = rows.map(row => 
      row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
    ).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  /**
   * Crea un backup dei dati correnti
   */
  static createBackup(gridData: BudgetGridRow[], budgetId: string): string {
    const backup = {
      budgetId,
      timestamp: new Date().toISOString(),
      data: gridData,
      version: '1.0',
    };

    const backupString = JSON.stringify(backup, null, 2);
    const backupKey = `budget-backup-${budgetId}-${Date.now()}`;
    
    try {
      localStorage.setItem(backupKey, backupString);
      return backupKey;
    } catch (error) {
      console.error('Failed to create backup:', error);
      throw new Error('Impossibile creare il backup: spazio di archiviazione insufficiente');
    }
  }

  /**
   * Ripristina i dati da un backup
   */
  static restoreFromBackup(backupKey: string): BudgetGridRow[] | null {
    try {
      const backupString = localStorage.getItem(backupKey);
      if (!backupString) {
        return null;
      }

      const backup = JSON.parse(backupString);
      return backup.data;
    } catch (error) {
      console.error('Failed to restore backup:', error);
      return null;
    }
  }

  /**
   * Lista tutti i backup disponibili per un budget
   */
  static listBackups(budgetId: string): Array<{ key: string; timestamp: string }> {
    const backups: Array<{ key: string; timestamp: string }> = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(`budget-backup-${budgetId}-`)) {
        try {
          const backupString = localStorage.getItem(key);
          if (backupString) {
            const backup = JSON.parse(backupString);
            backups.push({
              key,
              timestamp: backup.timestamp,
            });
          }
        } catch (error) {
          console.error('Invalid backup found:', key);
        }
      }
    }

    return backups.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }
}
