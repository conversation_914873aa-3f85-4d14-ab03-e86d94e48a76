// Real API implementation for Budget Management
const API_BASE_URL = 'http://localhost:3001/api/v1';

// Simple fetch wrapper
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`API call failed: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

export const realBudgetApi = {
  // Test endpoint
  test: () => apiCall('/test'),
  
  // Health check
  health: () => apiCall('/health'),

  // Budget operations
  getBudgets: (filters = {}) => {
    const params = new URLSearchParams(filters as any);
    return apiCall(`/budgets?${params}`);
  },

  getBudget: (id: string) => apiCall(`/budgets/${id}`),

  createBudget: (data: any) => apiCall('/budgets', {
    method: 'POST',
    body: JSON.stringify(data),
  }),

  updateBudget: (id: string, data: any) => apiCall(`/budgets/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),

  deleteBudget: (id: string) => apiCall(`/budgets/${id}`, {
    method: 'DELETE',
  }),

  // Budget workflow
  submitBudget: (id: string) => apiCall(`/budgets/${id}/submit`, {
    method: 'POST',
  }),

  approveBudgetAtPlant: (id: string) => apiCall(`/budgets/${id}/approve_plant`, {
    method: 'POST',
  }),

  approveBudgetAtHeadquarters: (id: string) => apiCall(`/budgets/${id}/approve_headquarters`, {
    method: 'POST',
  }),

  rejectBudget: (id: string, reason: string) => apiCall(`/budgets/${id}/reject`, {
    method: 'POST',
    body: JSON.stringify({ reason }),
  }),

  setAsReference: (id: string) => apiCall(`/budgets/${id}/set_reference`, {
    method: 'POST',
  }),

  // Budget calculations
  copyFromProposal: (id: string) => apiCall(`/budgets/${id}/copy_from_proposal`, {
    method: 'POST',
  }),

  calculateAutomatic: (id: string) => apiCall(`/budgets/${id}/calculate_automatic`, {
    method: 'POST',
  }),

  calculateDerived: (id: string) => apiCall(`/budgets/${id}/calculate_derived`, {
    method: 'POST',
  }),

  validateBudget: (id: string) => apiCall(`/budgets/${id}/validate`),

  // KPI operations
  getKPIs: () => apiCall('/kpis'),
  getKPICategories: () => apiCall('/kpis/categories'),

  // Plant operations
  getPlants: (type?: string) => {
    const params = type ? `?type=${type}` : '';
    return apiCall(`/plants${params}`);
  },

  getPlant: (id: string) => apiCall(`/plants/${id}`),

  getProductionUnits: (plantId: string) => apiCall(`/plants/${plantId}/production-units`),

  getMaintenancePlans: (plantId: string, year: number) => 
    apiCall(`/plants/maintenance-plans?plantId=${plantId}&year=${year}`),

  getMaintenanceInterventions: (planId: string) => 
    apiCall(`/plants/maintenance-interventions?planId=${planId}`),

  // Auth operations (for future use)
  login: (username: string, password: string) => apiCall('/auth/login', {
    method: 'POST',
    body: JSON.stringify({ username, password }),
  }),

  logout: () => apiCall('/auth/logout', {
    method: 'POST',
  }),

  getCurrentUser: () => apiCall('/auth/me'),
};
