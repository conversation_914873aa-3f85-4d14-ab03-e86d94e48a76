import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider, Layout, Menu, Button, Space, Typography } from 'antd';
import { BrowserRouter as Router, Routes, Route, Link, useNavigate } from 'react-router-dom';
import { PlusOutlined, UnorderedListOutlined, DashboardOutlined, SettingOutlined, DatabaseOutlined, ExperimentOutlined } from '@ant-design/icons';
import { CreateBudgetForm } from './components/CreateBudgetForm';
import { BudgetList } from './components/BudgetList';
import { BudgetDetail } from './components/BudgetDetail';
import { KPIManagement } from './components/KPIManagement';
import { DataViewer } from './components/DataViewer';
import { ApiTest } from './components/ApiTest';
import { Budget } from './types/budget';
import itIT from 'antd/locale/it_IT';
import 'dayjs/locale/it';

const { Header, Content, Sider } = Layout;
const { Title } = Typography;

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const BudgetDashboard: React.FC = () => {
  const navigate = useNavigate();

  const handleCreateSuccess = (budgetId: string) => {
    console.log('Budget created with ID:', budgetId);
    navigate('/budgets');
  };

  const handleViewBudget = (budget: Budget) => {
    navigate(`/budget/${budget.id}`);
  };

  const handleEditBudget = (budget: Budget) => {
    console.log('Edit budget:', budget);
    // Navigate to budget edit form
  };

  const handleDeleteBudget = (budget: Budget) => {
    console.log('Delete budget:', budget);
    // Show confirmation dialog and delete
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider width={250} theme="light">
        <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
          <Title level={4} style={{ margin: 0 }}>
            Budget Management
          </Title>
        </div>
        <Menu
          mode="inline"
          defaultSelectedKeys={['dashboard']}
          style={{ height: '100%', borderRight: 0 }}
          items={[
            {
              key: 'dashboard',
              icon: <DashboardOutlined />,
              label: <Link to="/">Dashboard</Link>,
            },
            {
              key: 'budgets',
              icon: <UnorderedListOutlined />,
              label: <Link to="/budgets">Lista Budget</Link>,
            },
            {
              key: 'create',
              icon: <PlusOutlined />,
              label: <Link to="/create">Crea Budget</Link>,
            },
            {
              key: 'kpi-management',
              icon: <SettingOutlined />,
              label: <Link to="/kpi-management">Gestione KPI</Link>,
            },
            {
              key: 'data-viewer',
              icon: <DatabaseOutlined />,
              label: <Link to="/data-viewer">Visualizza Dati</Link>,
            },
            {
              key: 'api-test',
              icon: <ExperimentOutlined />,
              label: <Link to="/api-test">Test API</Link>,
            },
          ]}
        />
      </Sider>
      
      <Layout>
        <Header style={{ background: '#fff', padding: '0 24px', borderBottom: '1px solid #f0f0f0' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={3} style={{ margin: 0 }}>
              Gestione Budget Annuale Impianti
            </Title>
            <Space>
              <Button type="primary" icon={<PlusOutlined />}>
                <Link to="/create">Nuovo Budget</Link>
              </Button>
            </Space>
          </div>
        </Header>
        
        <Content style={{ margin: '24px', background: '#f5f5f5' }}>
          <Routes>
            <Route 
              path="/" 
              element={
                <div style={{ padding: '24px', background: '#fff', borderRadius: '8px' }}>
                  <Title level={2}>Dashboard</Title>
                  <p>Benvenuto nel sistema di gestione budget annuale per impianti.</p>
                  <Space direction="vertical" size="large">
                    <Button type="primary" size="large" icon={<PlusOutlined />}>
                      <Link to="/create">Crea Nuovo Budget</Link>
                    </Button>
                    <Button size="large" icon={<UnorderedListOutlined />}>
                      <Link to="/budgets">Visualizza Budget Esistenti</Link>
                    </Button>
                  </Space>
                </div>
              } 
            />
            <Route 
              path="/create" 
              element={<CreateBudgetForm onSuccess={handleCreateSuccess} />} 
            />
            <Route
              path="/budgets"
              element={
                <BudgetList
                  onView={handleViewBudget}
                  onEdit={handleEditBudget}
                  onDelete={handleDeleteBudget}
                />
              }
            />
            <Route
              path="/budget/:id"
              element={<BudgetDetail />}
            />
            <Route
              path="/kpi-management"
              element={<KPIManagement />}
            />
            <Route
              path="/data-viewer"
              element={<DataViewer />}
            />
            <Route
              path="/api-test"
              element={<ApiTest />}
            />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
};

const App: React.FC = () => {
  return (
    <ConfigProvider locale={itIT}>
      <QueryClientProvider client={queryClient}>
        <Router>
          <BudgetDashboard />
        </Router>
      </QueryClientProvider>
    </ConfigProvider>
  );
};

export default App;
