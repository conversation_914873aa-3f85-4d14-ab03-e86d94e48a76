export interface Budget {
  id: string;
  plantId: string;
  productionUnitId: string;
  year: number;
  status: BudgetStatus;
  version: number;
  createdBy: string;
  createdAt: string;
  modifiedAt?: string;
  maintenancePlanId?: string;
  approvedAtPlantDate?: string;
  approvedAtPlantBy?: string;
  approvedAtHeadquartersDate?: string;
  approvedAtHeadquartersBy?: string;
  items: BudgetItem[];
}

export interface BudgetItem {
  id: string;
  kpiId: string;
  month: number;
  proposedValue?: number;
  budgetValue?: number;
  notes?: string;
  createdAt: string;
  modifiedAt?: string;
}

export interface KPI {
  id: string;
  code: string;
  name: string;
  unitOfMeasure: string;
  category: KPICategory;
  isAutoCalculated: boolean;
  calculationFormula?: string;
  displayOrder: number;
}

export interface KPICategory {
  id: string;
  name: string;
  color: string;
  displayOrder: number;
}

export interface BudgetGridRow {
  kpi: KPI;
  isAutoCalculated: boolean;
  monthlyValues: MonthlyBudgetValue[];
}

export interface MonthlyBudgetValue {
  month: number; // 0-11 (January-December)
  proposedValue?: number;
  budgetValue?: number;
}

export enum BudgetStatus {
  Draft = 'Draft',
  Submitted = 'Submitted',
  ApprovedPlant = 'ApprovedPlant',
  InApprovalHeadquarters = 'InApprovalHeadquarters',
  ApprovedHeadquarters = 'ApprovedHeadquarters',
  Reference = 'Reference',
  Rejected = 'Rejected'
}

export interface CreateBudgetRequest {
  plantId: string;
  productionUnitId: string;
  year: number;
  exerciseType?: PlantType;
}

export interface CreateBudgetResponse {
  budgetId: string;
  status: string;
  maintenancePlanInfo?: string;
  message?: string;
}

export interface Plant {
  id: string;
  name: string;
  type: PlantType;
  isActive: boolean;
}

export interface ProductionUnit {
  id: string;
  name: string;
  plantId: string;
  isActive: boolean;
}

export enum PlantType {
  Thermal = 'Thermal',
  Hydroelectric = 'Hydroelectric'
}

export interface UserProfile {
  id: string;
  userType: UserType;
  hasAccessToThermal: boolean;
  hasAccessToHydroelectric: boolean;
}

export enum UserType {
  Headquarters = 'Headquarters', // SEDE
  Cross = 'Cross',              // CROSS
  Thermal = 'Thermal',          // TERMO
  Hydroelectric = 'Hydroelectric' // IDRO
}

export interface MaintenancePlan {
  id: string;
  code: string;
  name: string;
  year: number;
  plantId: string;
  status: MaintenancePlanStatus;
  type: MaintenancePlanType;
  totalCost: number;
  createdAt: string;
  approvedAt?: string;
  isReference: boolean;
  interventions: MaintenanceIntervention[];
}

export enum MaintenancePlanStatus {
  Draft = 'Draft',
  Submitted = 'Submitted',
  Approved = 'Approved',
  Reference = 'Reference',
  ProBudget = 'ProBudget'
}

export enum MaintenancePlanType {
  Programmed = 'Programmed',
  Extraordinary = 'Extraordinary',
  Emergency = 'Emergency'
}

export interface MaintenanceIntervention {
  id: string;
  code: string;
  description: string;
  plannedMonth: number;
  estimatedCost: number;
  category: string;
  priority: InterventionPriority;
  equipment?: string;
}

export enum InterventionPriority {
  Low = 'Low',
  Medium = 'Medium',
  High = 'High',
  Critical = 'Critical'
}
