{"version": 3, "sources": ["../../dayjs/locale/it.js"], "sourcesContent": ["!function(e,o){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=o(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],o):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_it=o(e.dayjs)}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=o(e),n={name:\"it\",weekdays:\"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato\".split(\"_\"),weekdaysShort:\"dom_lun_mar_mer_gio_ven_sab\".split(\"_\"),weekdaysMin:\"do_lu_ma_me_gi_ve_sa\".split(\"_\"),months:\"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre\".split(\"_\"),weekStart:1,monthsShort:\"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic\".split(\"_\"),formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"tra %s\",past:\"%s fa\",s:\"qualche secondo\",m:\"un minuto\",mm:\"%d minuti\",h:\"un' ora\",hh:\"%d ore\",d:\"un giorno\",dd:\"%d giorni\",M:\"un mese\",MM:\"%d mesi\",y:\"un anno\",yy:\"%d anni\"},ordinal:function(e){return e+\"º\"}};return t.default.locale(n,null,!0),n}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,2DAA2D,MAAM,GAAG,GAAE,eAAc,8BAA8B,MAAM,GAAG,GAAE,aAAY,uBAAuB,MAAM,GAAG,GAAE,QAAO,gGAAgG,MAAM,GAAG,GAAE,WAAU,GAAE,aAAY,kDAAkD,MAAM,GAAG,GAAE,SAAQ,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,eAAc,KAAI,qBAAoB,MAAK,yBAAwB,GAAE,cAAa,EAAC,QAAO,UAAS,MAAK,SAAQ,GAAE,mBAAkB,GAAE,aAAY,IAAG,aAAY,GAAE,WAAU,IAAG,UAAS,GAAE,aAAY,IAAG,aAAY,GAAE,WAAU,IAAG,WAAU,GAAE,WAAU,IAAG,UAAS,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA,KAAE;AAAA,MAAG,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e"]}